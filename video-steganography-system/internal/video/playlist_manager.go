package video

import (
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"regexp"
	"sort"
	"strings"
	"sync"
	"time"

	"github.com/sirupsen/logrus"
)

// sanitizePlaylistID 清理和验证播放列表ID以防止路径遍历攻击
func sanitizePlaylistID(id string) (string, error) {
	// 移除危险字符
	cleaned := strings.ReplaceAll(id, "..", "")
	cleaned = strings.ReplaceAll(cleaned, "/", "")
	cleaned = strings.ReplaceAll(cleaned, "\\", "")
	cleaned = strings.ReplaceAll(cleaned, "\x00", "") // 空字节

	if cleaned != id {
		return "", fmt.Errorf("invalid playlist ID contains dangerous characters")
	}

	// 限制长度
	if len(cleaned) > 100 {
		return "", fmt.Errorf("playlist ID too long")
	}

	// 只允许字母数字和安全字符
	matched, _ := regexp.MatchString(`^[a-zA-Z0-9_-]+$`, cleaned)
	if !matched {
		return "", fmt.<PERSON><PERSON>rf("playlist ID contains invalid characters")
	}

	return cleaned, nil
}

// PlaylistManager 播放列表管理器
type PlaylistManager struct {
	playlists    map[string]*ManagedPlaylist
	mu           sync.RWMutex
	logger       *logrus.Logger
	storageDir   string
	hlsGenerator *HLSGenerator
}

// ManagedPlaylist 管理的播放列表
type ManagedPlaylist struct {
	ID           string                 `json:"id"`
	JobID        string                 `json:"job_id"`
	Playlist     *HLSPlaylist           `json:"playlist"`
	CreatedAt    time.Time              `json:"created_at"`
	UpdatedAt    time.Time              `json:"updated_at"`
	AccessCount  int64                  `json:"access_count"`
	LastAccessed time.Time              `json:"last_accessed"`
	Metadata     map[string]interface{} `json:"metadata"`
	Status       PlaylistStatus         `json:"status"`
	FilePath     string                 `json:"file_path"`
	BaseURL      string                 `json:"base_url"`
}

// PlaylistStatus 播放列表状态
type PlaylistStatus string

const (
	PlaylistStatusActive   PlaylistStatus = "active"
	PlaylistStatusInactive PlaylistStatus = "inactive"
	PlaylistStatusExpired  PlaylistStatus = "expired"
	PlaylistStatusError    PlaylistStatus = "error"
)

// NewPlaylistManager 创建播放列表管理器
func NewPlaylistManager(storageDir string, hlsGenerator *HLSGenerator, logger *logrus.Logger) *PlaylistManager {
	pm := &PlaylistManager{
		playlists:    make(map[string]*ManagedPlaylist),
		logger:       logger,
		storageDir:   storageDir,
		hlsGenerator: hlsGenerator,
	}

	// 创建存储目录
	if err := os.MkdirAll(storageDir, 0755); err != nil {
		logger.Errorf("Failed to create playlist storage directory: %v", err)
	}

	// 加载已存在的播放列表
	if err := pm.loadExistingPlaylists(); err != nil {
		logger.Warnf("Failed to load existing playlists: %v", err)
	}

	logger.Info("Playlist manager initialized")
	return pm
}

// CreatePlaylist 创建播放列表
func (pm *PlaylistManager) CreatePlaylist(jobID string, segments []SegmentInfo, options ProcessingOptions, baseURL string) (*ManagedPlaylist, error) {
	pm.mu.Lock()
	defer pm.mu.Unlock()

	// 生成播放列表
	playlist, err := pm.hlsGenerator.GeneratePlaylist(segments, options)
	if err != nil {
		return nil, fmt.Errorf("failed to generate playlist: %w", err)
	}

	// 更新URL
	pm.hlsGenerator.UpdatePlaylistURLs(playlist, baseURL)

	// 创建管理的播放列表
	managed := &ManagedPlaylist{
		ID:           jobID, // 使用jobID作为播放列表ID
		JobID:        jobID,
		Playlist:     playlist,
		CreatedAt:    time.Now(),
		UpdatedAt:    time.Now(),
		AccessCount:  0,
		LastAccessed: time.Now(),
		Metadata:     make(map[string]interface{}),
		Status:       PlaylistStatusActive,
		BaseURL:      baseURL,
	}

	// 验证和清理播放列表ID以防止路径遍历攻击
	cleanID, err := sanitizePlaylistID(managed.ID)
	if err != nil {
		return nil, fmt.Errorf("invalid playlist ID: %w", err)
	}

	// 设置文件路径
	managed.FilePath = filepath.Join(pm.storageDir, fmt.Sprintf("%s.m3u8", cleanID))

	// 写入文件
	if err := pm.hlsGenerator.WritePlaylistFile(playlist, managed.FilePath); err != nil {
		return nil, fmt.Errorf("failed to write playlist file: %w", err)
	}

	// 保存元数据
	if err := pm.savePlaylistMetadata(managed); err != nil {
		pm.logger.Warnf("Failed to save playlist metadata: %v", err)
	}

	// 存储到内存
	pm.playlists[managed.ID] = managed

	pm.logger.Infof("Created playlist %s for job %s", managed.ID, jobID)
	return managed, nil
}

// GetPlaylist 获取播放列表
func (pm *PlaylistManager) GetPlaylist(playlistID string) (*ManagedPlaylist, error) {
	pm.mu.RLock()
	defer pm.mu.RUnlock()

	playlist, exists := pm.playlists[playlistID]
	if !exists {
		return nil, fmt.Errorf("playlist not found: %s", playlistID)
	}

	// 更新访问统计
	playlist.AccessCount++
	playlist.LastAccessed = time.Now()

	// 返回副本
	playlistCopy := *playlist
	return &playlistCopy, nil
}

// GetPlaylistContent 获取播放列表内容
func (pm *PlaylistManager) GetPlaylistContent(playlistID string) (string, error) {
	playlist, err := pm.GetPlaylist(playlistID)
	if err != nil {
		return "", err
	}

	// 检查文件是否存在
	if _, err := os.Stat(playlist.FilePath); os.IsNotExist(err) {
		// 重新生成文件
		if err := pm.hlsGenerator.WritePlaylistFile(playlist.Playlist, playlist.FilePath); err != nil {
			return "", fmt.Errorf("failed to regenerate playlist file: %w", err)
		}
	}

	// 读取文件内容
	content, err := os.ReadFile(playlist.FilePath)
	if err != nil {
		return "", fmt.Errorf("failed to read playlist file: %w", err)
	}

	return string(content), nil
}

// UpdatePlaylist 更新播放列表
func (pm *PlaylistManager) UpdatePlaylist(playlistID string, segments []SegmentInfo, options ProcessingOptions) error {
	pm.mu.Lock()
	defer pm.mu.Unlock()

	managed, exists := pm.playlists[playlistID]
	if !exists {
		return fmt.Errorf("playlist not found: %s", playlistID)
	}

	// 重新生成播放列表
	playlist, err := pm.hlsGenerator.GeneratePlaylist(segments, options)
	if err != nil {
		return fmt.Errorf("failed to generate playlist: %w", err)
	}

	// 更新URL
	pm.hlsGenerator.UpdatePlaylistURLs(playlist, managed.BaseURL)

	// 更新管理的播放列表
	managed.Playlist = playlist
	managed.UpdatedAt = time.Now()

	// 写入文件
	if err := pm.hlsGenerator.WritePlaylistFile(playlist, managed.FilePath); err != nil {
		return fmt.Errorf("failed to write playlist file: %w", err)
	}

	// 保存元数据
	if err := pm.savePlaylistMetadata(managed); err != nil {
		pm.logger.Warnf("Failed to save playlist metadata: %v", err)
	}

	pm.logger.Infof("Updated playlist %s", playlistID)
	return nil
}

// DeletePlaylist 删除播放列表
func (pm *PlaylistManager) DeletePlaylist(playlistID string) error {
	pm.mu.Lock()
	defer pm.mu.Unlock()

	managed, exists := pm.playlists[playlistID]
	if !exists {
		return fmt.Errorf("playlist not found: %s", playlistID)
	}

	// 删除文件
	if err := os.Remove(managed.FilePath); err != nil && !os.IsNotExist(err) {
		pm.logger.Warnf("Failed to delete playlist file: %v", err)
	}

	// 删除元数据文件
	metadataPath := pm.getMetadataPath(playlistID)
	if err := os.Remove(metadataPath); err != nil && !os.IsNotExist(err) {
		pm.logger.Warnf("Failed to delete playlist metadata: %v", err)
	}

	// 从内存中删除
	delete(pm.playlists, playlistID)

	pm.logger.Infof("Deleted playlist %s", playlistID)
	return nil
}

// ListPlaylists 列出播放列表
func (pm *PlaylistManager) ListPlaylists() []*ManagedPlaylist {
	pm.mu.RLock()
	defer pm.mu.RUnlock()

	var playlists []*ManagedPlaylist
	for _, playlist := range pm.playlists {
		playlistCopy := *playlist
		playlists = append(playlists, &playlistCopy)
	}

	// 按创建时间排序
	sort.Slice(playlists, func(i, j int) bool {
		return playlists[i].CreatedAt.After(playlists[j].CreatedAt)
	})

	return playlists
}

// GetPlaylistStats 获取播放列表统计
func (pm *PlaylistManager) GetPlaylistStats() map[string]interface{} {
	pm.mu.RLock()
	defer pm.mu.RUnlock()

	stats := map[string]interface{}{
		"total_playlists": len(pm.playlists),
		"status_counts":   make(map[PlaylistStatus]int),
		"total_accesses":  int64(0),
	}

	statusCounts := make(map[PlaylistStatus]int)
	var totalAccesses int64

	for _, playlist := range pm.playlists {
		statusCounts[playlist.Status]++
		totalAccesses += playlist.AccessCount
	}

	stats["status_counts"] = statusCounts
	stats["total_accesses"] = totalAccesses

	return stats
}

// CleanupExpiredPlaylists 清理过期播放列表
func (pm *PlaylistManager) CleanupExpiredPlaylists(maxAge time.Duration) int {
	pm.mu.Lock()
	defer pm.mu.Unlock()

	cutoff := time.Now().Add(-maxAge)
	var toDelete []string

	for id, playlist := range pm.playlists {
		if playlist.LastAccessed.Before(cutoff) {
			toDelete = append(toDelete, id)
		}
	}

	for _, id := range toDelete {
		managed := pm.playlists[id]

		// 删除文件
		if err := os.Remove(managed.FilePath); err != nil && !os.IsNotExist(err) {
			pm.logger.Warnf("Failed to delete playlist file: %v", err)
		}

		// 删除元数据
		metadataPath := pm.getMetadataPath(id)
		if err := os.Remove(metadataPath); err != nil && !os.IsNotExist(err) {
			pm.logger.Warnf("Failed to delete playlist metadata: %v", err)
		}

		delete(pm.playlists, id)
	}

	if len(toDelete) > 0 {
		pm.logger.Infof("Cleaned up %d expired playlists", len(toDelete))
	}

	return len(toDelete)
}

// savePlaylistMetadata 保存播放列表元数据
func (pm *PlaylistManager) savePlaylistMetadata(managed *ManagedPlaylist) error {
	metadataPath := pm.getMetadataPath(managed.ID)

	data, err := json.MarshalIndent(managed, "", "  ")
	if err != nil {
		return err
	}

	return os.WriteFile(metadataPath, data, 0644)
}

// loadExistingPlaylists 加载已存在的播放列表
func (pm *PlaylistManager) loadExistingPlaylists() error {
	pattern := filepath.Join(pm.storageDir, "*.json")
	files, err := filepath.Glob(pattern)
	if err != nil {
		return err
	}

	for _, file := range files {
		if err := pm.loadPlaylistFromFile(file); err != nil {
			pm.logger.Warnf("Failed to load playlist from %s: %v", file, err)
		}
	}

	pm.logger.Infof("Loaded %d existing playlists", len(pm.playlists))
	return nil
}

// loadPlaylistFromFile 从文件加载播放列表
func (pm *PlaylistManager) loadPlaylistFromFile(filePath string) error {
	data, err := os.ReadFile(filePath)
	if err != nil {
		return err
	}

	var managed ManagedPlaylist
	if err := json.Unmarshal(data, &managed); err != nil {
		return err
	}

	// 检查播放列表文件是否存在
	if _, err := os.Stat(managed.FilePath); os.IsNotExist(err) {
		pm.logger.Warnf("Playlist file not found: %s", managed.FilePath)
		return nil
	}

	pm.playlists[managed.ID] = &managed
	return nil
}

// getMetadataPath 获取元数据文件路径
func (pm *PlaylistManager) getMetadataPath(playlistID string) string {
	return filepath.Join(pm.storageDir, fmt.Sprintf("%s.json", playlistID))
}

// ValidatePlaylistIntegrity 验证播放列表完整性
func (pm *PlaylistManager) ValidatePlaylistIntegrity(playlistID string) error {
	playlist, err := pm.GetPlaylist(playlistID)
	if err != nil {
		return err
	}

	// 验证播放列表文件
	if _, err := os.Stat(playlist.FilePath); os.IsNotExist(err) {
		return fmt.Errorf("playlist file not found: %s", playlist.FilePath)
	}

	// 验证切片文件
	for i, segment := range playlist.Playlist.Segments {
		if _, err := os.Stat(segment.FilePath); os.IsNotExist(err) {
			return fmt.Errorf("segment %d file not found: %s", i, segment.FilePath)
		}
	}

	return nil
}

// GetPlaylistByJobID 根据任务ID获取播放列表
func (pm *PlaylistManager) GetPlaylistByJobID(jobID string) (*ManagedPlaylist, error) {
	pm.mu.RLock()
	defer pm.mu.RUnlock()

	for _, playlist := range pm.playlists {
		if playlist.JobID == jobID {
			playlistCopy := *playlist
			return &playlistCopy, nil
		}
	}

	return nil, fmt.Errorf("playlist not found for job: %s", jobID)
}

// UpdatePlaylistUrls 更新播放列表中的URL
func (pm *PlaylistManager) UpdatePlaylistUrls(playlistID, baseURL string) error {
	pm.mu.Lock()
	defer pm.mu.Unlock()

	playlist, exists := pm.playlists[playlistID]
	if !exists {
		return fmt.Errorf("playlist not found: %s", playlistID)
	}

	// 更新基础URL
	playlist.BaseURL = baseURL

	// 更新所有切片的URL
	for i := range playlist.Playlist.Segments {
		segmentName := filepath.Base(playlist.Playlist.Segments[i].FilePath)
		playlist.Playlist.Segments[i].URL = baseURL + segmentName
	}

	// 更新时间戳
	playlist.UpdatedAt = time.Now()

	// 重新生成M3U8文件
	if err := pm.hlsGenerator.WritePlaylistFile(playlist.Playlist, playlist.FilePath); err != nil {
		return fmt.Errorf("failed to write playlist file: %w", err)
	}

	// 保存元数据
	if err := pm.savePlaylistMetadata(playlist); err != nil {
		return fmt.Errorf("failed to save playlist metadata: %w", err)
	}

	pm.logger.Infof("Updated playlist URLs for %s with base URL: %s", playlistID, baseURL)
	return nil
}

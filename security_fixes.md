# Make Server 安全漏洞修复方案

## 🚨 关键漏洞修复

### 1. 修复FFmpeg错误处理 - 最严重
**文件**: `pkg/tiktok_steganography/video_segmenter.go:476-482`

**问题**: 强制返回成功导致处理损坏视频片段
```go
// 当前代码（有问题）
return nil // 永远返回成功，确保处理继续
```

**修复方案**:
```go
err = cmd.Wait()
if err != nil {
    vs.logger.Errorf("❌ FFmpeg进程异常退出: %v", err)
    
    // 检查是否有可用的片段文件
    segmentFiles, scanErr := vs.scanSegmentFiles(outputDir)
    if scanErr != nil || len(segmentFiles) == 0 {
        return fmt.Errorf("ffmpeg failed and no valid segments generated: %w", err)
    }
    
    // 验证片段文件完整性
    validSegments := 0
    for _, segFile := range segmentFiles {
        if stat, err := os.Stat(segFile); err == nil && stat.Size() > 1024 {
            validSegments++
        }
    }
    
    if validSegments == 0 {
        return fmt.Errorf("ffmpeg failed and no valid segments found: %w", err)
    }
    
    vs.logger.Warnf("⚠️ FFmpeg有错误但生成了%d个有效片段，继续处理", validSegments)
}
return nil
```

### 2. 修复路径遍历攻击
**文件**: `video-steganography-system/internal/video/playlist_manager.go:101`

**问题**: managed.ID未验证，可能包含../等危险字符

**修复方案**:
```go
// 添加安全验证函数
func sanitizePlaylistID(id string) (string, error) {
    // 移除危险字符
    cleaned := strings.ReplaceAll(id, "..", "")
    cleaned = strings.ReplaceAll(cleaned, "/", "")
    cleaned = strings.ReplaceAll(cleaned, "\\", "")
    cleaned = strings.ReplaceAll(cleaned, "\x00", "") // 空字节
    
    if cleaned != id {
        return "", fmt.Errorf("invalid playlist ID contains dangerous characters")
    }
    
    // 限制长度和字符集
    if len(cleaned) > 100 {
        return "", fmt.Errorf("playlist ID too long")
    }
    
    // 只允许字母数字和安全字符
    matched, _ := regexp.MatchString(`^[a-zA-Z0-9_-]+$`, cleaned)
    if !matched {
        return "", fmt.Errorf("playlist ID contains invalid characters")
    }
    
    return cleaned, nil
}

// 使用时：
cleanID, err := sanitizePlaylistID(managed.ID)
if err != nil {
    return nil, fmt.Errorf("invalid playlist ID: %w", err)
}
managed.FilePath = filepath.Join(pm.storageDir, fmt.Sprintf("%s.m3u8", cleanID))
```

### 3. 修复信号量泄漏
**文件**: `pkg/tiktok_steganography/cdn_uploader.go:448-454`

**问题**: panic时信号量不释放导致死锁

**修复方案**:
```go
// 获取信号量
cu.semaphore <- struct{}{}

// 确保信号量总是被释放
defer func() {
    <-cu.semaphore
}()

// 执行上传（带重试）
result := cu.uploadWithRetry(ctx, task)
```

### 4. 修复内存耗尽攻击
**文件**: `pkg/tiktok_steganography/png_disguiser.go:186`

**问题**: 没有大小限制，可能导致OOM

**修复方案**:
```go
const MaxPNGSize = 100 * 1024 * 1024 // 100MB限制
const MaxSegmentSize = 50 * 1024 * 1024 // 50MB单个片段限制

// 检查输入文件大小
if len(disguisedData) > MaxSegmentSize {
    return nil, fmt.Errorf("segment too large: %d bytes (max: %d)", 
        len(disguisedData), MaxSegmentSize)
}

totalSize := len(pd.pngHeader) + len(disguisedData)
if totalSize > MaxPNGSize {
    return nil, fmt.Errorf("PNG size too large: %d bytes (max: %d)", 
        totalSize, MaxPNGSize)
}

pngData = make([]byte, totalSize)
copy(pngData, pd.pngHeader)
copy(pngData[len(pd.pngHeader):], disguisedData)
```

### 5. 修复文件权限过于宽松
**文件**: 多个文件中的权限设置

**问题**: 使用0755/0644权限，其他用户可读

**修复方案**:
```go
// 目录权限：只有所有者可访问
os.MkdirAll(outputDir, 0700)

// 文件权限：只有所有者可读写
os.WriteFile(playlistPath, []byte(playlist), 0600)

// 临时文件权限
tempFile, err := os.OpenFile(tempPath, os.O_CREATE|os.O_WRONLY|os.O_TRUNC, 0600)
```

## 🔧 中等风险修复

### 6. 修复并发竞态条件
**文件**: `pkg/tiktok_steganography/worker_pool.go`

**修复方案**:
```go
// 为stats添加一致的锁保护
func (wp *WorkerPool) IncrementTasks() {
    wp.mu.Lock()
    defer wp.mu.Unlock()
    wp.stats.TotalTasks++
}

func (wp *WorkerPool) GetStats() *WorkerPoolStats {
    wp.mu.RLock()
    defer wp.mu.RUnlock()
    
    // 返回副本避免并发访问
    return &WorkerPoolStats{
        TotalTasks:    wp.stats.TotalTasks,
        CompletedTasks: wp.stats.CompletedTasks,
        FailedTasks:   wp.stats.FailedTasks,
        StartTime:     wp.stats.StartTime,
    }
}
```

### 7. 添加输入验证
**文件**: `pkg/tiktok_steganography/video_segmenter.go`

**修复方案**:
```go
func (vs *videoSegmenter) ValidateVideo(videoPath string) error {
    // 检查文件路径安全性
    cleanPath := filepath.Clean(videoPath)
    if cleanPath != videoPath {
        return fmt.Errorf("unsafe file path")
    }
    
    // 检查文件大小
    stat, err := os.Stat(videoPath)
    if err != nil {
        return fmt.Errorf("cannot stat video file: %w", err)
    }
    
    const MaxVideoSize = 10 * 1024 * 1024 * 1024 // 10GB
    if stat.Size() > MaxVideoSize {
        return fmt.Errorf("video file too large: %d bytes", stat.Size())
    }
    
    // 检查文件格式
    cmd := exec.Command("ffprobe", "-v", "quiet", "-print_format", "json", 
        "-show_format", videoPath)
    output, err := cmd.Output()
    if err != nil {
        return fmt.Errorf("invalid video file format: %w", err)
    }
    
    // 验证是否为有效视频
    var format struct {
        Format struct {
            FormatName string `json:"format_name"`
            Duration   string `json:"duration"`
        } `json:"format"`
    }
    
    if err := json.Unmarshal(output, &format); err != nil {
        return fmt.Errorf("cannot parse video format: %w", err)
    }
    
    if format.Format.FormatName == "" {
        return fmt.Errorf("invalid video format")
    }
    
    return nil
}
```

## 📋 修复优先级

### 🔴 立即修复（今天）
1. **FFmpeg错误处理** - 影响M3U8播放的根本原因
2. **路径遍历攻击** - 严重安全漏洞
3. **信号量泄漏** - 可能导致系统死锁

### 🟡 紧急修复（本周）
4. **内存耗尽攻击** - 可能导致服务器崩溃
5. **文件权限** - 数据泄露风险
6. **并发竞态条件** - 数据一致性问题

### 🟢 重要修复（下周）
7. **输入验证** - 防止各种注入攻击
8. **访问控制** - API安全加固
9. **资源泄漏** - 长期稳定性

## 🧪 测试建议

1. **损坏视频测试**: 使用截断的视频文件测试错误处理
2. **路径遍历测试**: 尝试使用../../../etc/passwd等路径
3. **并发压力测试**: 高并发场景下的稳定性
4. **大文件测试**: 测试内存限制的有效性
5. **权限测试**: 验证文件权限设置正确

## ⚠️ 重要提醒

**FFmpeg错误强制忽略是导致M3U8播放问题的根本原因**，必须立即修复！这个漏洞导致系统处理损坏的视频片段，生成无法播放的M3U8文件。

package tiktok_steganography

import (
	"bytes"
	"context"
	"fmt"
	"io/ioutil"
	"os"
	"path/filepath"
	"strings"
	"sync"
	"sync/atomic"

	"github.com/sirupsen/logrus"
)

const (
	MaxPNGSize     = 100 * 1024 * 1024 // 100MB PNG文件大小限制
	MaxSegmentSize = 50 * 1024 * 1024  // 50MB单个片段大小限制
)

// pngDisguiser PNG伪装器实现
type pngDisguiser struct {
	config    *DisguiseConfig
	logger    *logrus.Logger
	pngHeader []byte
	semaphore chan struct{}
	mu        sync.RWMutex
}

// NewPNGDisguiser 创建新的PNG伪装器
func NewPNGDisguiser(config *DisguiseConfig, logger *logrus.Logger) (PNGDisguiser, error) {
	if config == nil {
		return nil, fmt.Errorf("config cannot be nil")
	}
	if logger == nil {
		return nil, fmt.Errorf("logger cannot be nil")
	}

	disguiser := &pngDisguiser{
		config:    config,
		logger:    logger,
		semaphore: make(chan struct{}, config.ConcurrentWorkers),
	}

	// 创建标准PNG头部
	if err := disguiser.initializePNGHeader(); err != nil {
		return nil, fmt.Errorf("failed to initialize PNG header: %w", err)
	}

	return disguiser, nil
}

// DisguiseSegments 批量伪装TS切片为PNG
func (pd *pngDisguiser) DisguiseSegments(ctx context.Context, segmentPaths []string, outputDir string) (*DisguiseResult, error) {
	pd.logger.Infof("🎭 开始批量PNG伪装: %d个文件", len(segmentPaths))

	if len(segmentPaths) == 0 {
		return nil, fmt.Errorf("no segment paths provided")
	}

	// 创建输出目录（使用安全权限）
	if err := os.MkdirAll(outputDir, 0700); err != nil {
		return nil, fmt.Errorf("failed to create output directory: %w", err)
	}

	// 创建任务通道
	taskChan := make(chan *disguiseTask, len(segmentPaths))
	resultChan := make(chan *disguiseTaskResult, len(segmentPaths))

	// 启动worker goroutines
	var wg sync.WaitGroup
	for i := 0; i < pd.config.ConcurrentWorkers; i++ {
		wg.Add(1)
		go func() {
			defer wg.Done()
			pd.disguiseWorker(ctx, taskChan, resultChan)
		}()
	}

	// 发送任务
	for i, segmentPath := range segmentPaths {
		baseName := filepath.Base(segmentPath)
		outputName := pd.generatePNGFilename(baseName)
		outputPath := filepath.Join(outputDir, outputName)

		taskChan <- &disguiseTask{
			Index:      i,
			InputPath:  segmentPath,
			OutputPath: outputPath,
			XORKey:     pd.config.XORKey,
		}
	}
	close(taskChan)

	// 等待所有worker完成
	go func() {
		wg.Wait()
		close(resultChan)
	}()

	// 收集结果
	var disguisedPaths []string
	var successCount, failedCount int32

	results := make([]*disguiseTaskResult, len(segmentPaths))
	for result := range resultChan {
		results[result.Task.Index] = result
		if result.Success {
			atomic.AddInt32(&successCount, 1)
		} else {
			atomic.AddInt32(&failedCount, 1)
			pd.logger.Errorf("伪装失败: %s -> %v", result.Task.InputPath, result.Error)
		}
	}

	// 🔧 修复：按Index顺序处理结果，保持索引对应关系
	disguisedPaths = make([]string, len(segmentPaths))
	for i, result := range results {
		if result != nil && result.Success {
			disguisedPaths[i] = result.Task.OutputPath
			pd.logger.Debugf("✅ 伪装成功: Index=%d, %s -> %s",
				i, filepath.Base(result.Task.InputPath), filepath.Base(result.Task.OutputPath))
		} else {
			// 失败的任务保持空字符串，但保留索引位置
			disguisedPaths[i] = ""
			if result != nil {
				pd.logger.Warnf("❌ 伪装失败: Index=%d, %s -> %v",
					i, filepath.Base(result.Task.InputPath), result.Error)
			}
		}
	}

	result := &DisguiseResult{
		DisguisedPaths: disguisedPaths,
		SuccessCount:   int(successCount),
		FailedCount:    int(failedCount),
		XORKey:         pd.config.XORKey,
		PNGHeaderSize:  len(pd.pngHeader),
	}

	pd.logger.Infof("✅ PNG伪装完成: %d/%d 成功", result.SuccessCount, len(segmentPaths))
	return result, nil
}

// DisguiseSegment 伪装单个TS切片
func (pd *pngDisguiser) DisguiseSegment(segmentPath, outputPath string) (*DisguiseInfo, error) {
	// 检查文件大小以防止内存耗尽攻击
	fileInfo, err := os.Stat(segmentPath)
	if err != nil {
		return nil, fmt.Errorf("failed to stat segment file: %w", err)
	}

	if fileInfo.Size() > MaxSegmentSize {
		return nil, fmt.Errorf("segment file too large: %d bytes (max: %d)", fileInfo.Size(), MaxSegmentSize)
	}

	// 读取视频数据（支持TS和fMP4格式）
	videoData, err := ioutil.ReadFile(segmentPath)
	if err != nil {
		return nil, fmt.Errorf("failed to read segment file: %w", err)
	}

	// 检测文件格式
	fileFormat := "TS"
	if strings.HasSuffix(segmentPath, ".m4s") || strings.Contains(segmentPath, "init.mp4") {
		fileFormat = "fMP4"
	}

	// 应用XOR伪装
	pd.logger.Infof("🔐 应用XOR伪装: 文件=%s, 格式=%s, 原始大小=%d字节, XOR密钥=%d",
		filepath.Base(segmentPath), fileFormat, len(videoData), pd.config.XORKey)
	disguisedData := pd.ApplyXORDisguise(videoData, pd.config.XORKey)

	// 🔧 关键修复：使用TikTok参考文件的完整结构
	var pngData []byte
	refFile := "/tmp/tiktok_reference.png"
	refData, err := ioutil.ReadFile(refFile)
	if err != nil {
		pd.logger.Warnf("⚠️ 无法读取TikTok参考文件，使用简单PNG头部: %v", err)
		// 回退到简单PNG头部方法
		pngData = make([]byte, len(pd.pngHeader)+len(disguisedData))
		copy(pngData, pd.pngHeader)
		copy(pngData[len(pd.pngHeader):], disguisedData)
	} else {
		// 使用TikTok参考文件的完整结构
		pd.logger.Infof("📋 使用TikTok参考文件结构: %d字节", len(refData))

		// 找到IEND块后的数据开始位置
		iendPos := bytes.Index(refData, []byte("IEND"))
		if iendPos == -1 {
			pd.logger.Warnf("⚠️ TikTok参考文件无效，使用简单PNG头部")
			pngData = make([]byte, len(pd.pngHeader)+len(disguisedData))
			copy(pngData, pd.pngHeader)
			copy(pngData[len(pd.pngHeader):], disguisedData)
		} else {
			// 🚨 紧急修复：确保视频数据被正确嵌入，不能丢失数据！
			pd.logger.Infof("🔧 使用PNG头部+%s数据追加方法（确保数据完整性）", fileFormat)

			// 检查最终PNG大小以防止内存耗尽
			totalSize := len(pd.pngHeader) + len(disguisedData)
			if totalSize > MaxPNGSize {
				return nil, fmt.Errorf("PNG size too large: %d bytes (max: %d)", totalSize, MaxPNGSize)
			}

			// 使用简单但可靠的PNG头部+数据追加方法
			pngData = make([]byte, totalSize)
			copy(pngData, pd.pngHeader)
			copy(pngData[len(pd.pngHeader):], disguisedData)

			pd.logger.Infof("📦 PNG伪装完成: PNG头部=%d字节, %s数据=%d字节, 总大小=%d字节",
				len(pd.pngHeader), fileFormat, len(disguisedData), len(pngData))

			// 验证数据完整性
			if len(pngData) != len(pd.pngHeader)+len(disguisedData) {
				pd.logger.Errorf("❌ PNG数据大小异常: 期望=%d, 实际=%d",
					len(pd.pngHeader)+len(disguisedData), len(pngData))
			}
		}
	}

	pd.logger.Infof("📦 创建PNG文件: PNG头部=%d字节, 加密数据=%d字节, 总大小=%d字节",
		len(pd.pngHeader), len(disguisedData), len(pngData))

	// 写入输出文件
	if err := ioutil.WriteFile(outputPath, pngData, 0644); err != nil {
		return nil, fmt.Errorf("failed to write PNG file: %w", err)
	}

	pd.logger.Infof("✅ PNG伪装文件已保存: %s", outputPath)

	// 计算存储效率
	efficiency := float64(len(videoData)) / float64(len(pngData))

	info := &DisguiseInfo{
		OriginalPath:  segmentPath,
		DisguisedPath: outputPath,
		OriginalSize:  int64(len(videoData)),
		DisguisedSize: int64(len(pngData)),
		XORKey:        pd.config.XORKey,
		PNGHeaderSize: len(pd.pngHeader),
		Efficiency:    efficiency,
	}

	return info, nil
}

// CreatePNGHeader 创建PNG头部
func (pd *pngDisguiser) CreatePNGHeader() ([]byte, error) {
	pd.mu.RLock()
	defer pd.mu.RUnlock()

	if pd.pngHeader == nil {
		return nil, fmt.Errorf("PNG header not initialized")
	}

	// 返回副本
	header := make([]byte, len(pd.pngHeader))
	copy(header, pd.pngHeader)
	return header, nil
}

// ApplyXORDisguise 应用XOR伪装
func (pd *pngDisguiser) ApplyXORDisguise(data []byte, key int) []byte {
	if key < 0 || key > 255 {
		pd.logger.Warnf("Invalid XOR key %d, using default %d", key, DefaultXORKey)
		key = DefaultXORKey
	}

	disguised := make([]byte, len(data))
	keyByte := byte(key)

	for i, b := range data {
		disguised[i] = b ^ keyByte
	}

	return disguised
}

// GetDisguiseInfo 获取伪装信息
func (pd *pngDisguiser) GetDisguiseInfo(originalSize, disguisedSize int64, xorKey int) *DisguiseInfo {
	efficiency := float64(originalSize) / float64(disguisedSize)

	return &DisguiseInfo{
		OriginalSize:  originalSize,
		DisguisedSize: disguisedSize,
		XORKey:        xorKey,
		PNGHeaderSize: len(pd.pngHeader),
		Efficiency:    efficiency,
	}
}

// initializePNGHeader 初始化PNG头部（使用TikTok参考文件）
func (pd *pngDisguiser) initializePNGHeader() error {
	// 🔧 修复：强制使用TikTok参考文件的完整PNG头部
	refFile := "/tmp/tiktok_reference.png"

	// 检查参考文件是否存在
	if _, err := os.Stat(refFile); os.IsNotExist(err) {
		pd.logger.Errorf("❌ TikTok参考文件不存在: %s", refFile)
		return fmt.Errorf("TikTok参考文件不存在，PNG伪装无法工作")
	}

	// 读取TikTok参考PNG文件
	data, err := ioutil.ReadFile(refFile)
	if err != nil {
		pd.logger.Errorf("❌ 读取TikTok参考文件失败: %v", err)
		return fmt.Errorf("读取TikTok参考文件失败: %w", err)
	}

	// 🔧 关键修复：查找IEND块的位置来确定PNG的实际结束位置
	iendPos := bytes.Index(data, []byte("IEND"))
	if iendPos == -1 {
		pd.logger.Errorf("❌ TikTok参考文件无效（未找到IEND）")
		return fmt.Errorf("TikTok参考文件无效：未找到IEND块")
	}

	// PNG头部应该包括从开始到IEND块结束（IEND + 4字节CRC）
	pngHeaderEnd := iendPos + 8 // IEND(4) + CRC(4)
	if pngHeaderEnd > len(data) {
		pd.logger.Errorf("❌ TikTok参考文件截断")
		return fmt.Errorf("TikTok参考文件截断")
	}

	// 🔧 关键修复：只使用PNG的完整头部，确保是有效的PNG文件
	pd.pngHeader = make([]byte, pngHeaderEnd)
	copy(pd.pngHeader, data[:pngHeaderEnd])

	// 验证PNG头部的有效性
	if !pd.validatePNGHeader() {
		pd.logger.Errorf("❌ 提取的PNG头部无效")
		return fmt.Errorf("提取的PNG头部无效")
	}

	pd.logger.Infof("✅ TikTok完美PNG头部初始化完成: %d字节 (提取自参考文件，原文件%d字节)",
		len(pd.pngHeader), len(data))
	return nil
}

// initializeSimplePNGHeader 初始化简单PNG头部（备用方案）
func (pd *pngDisguiser) initializeSimplePNGHeader() error {
	// 创建标准212字节PNG头部（符合TikTok CDN要求）
	// 这是一个最小的有效PNG文件头部
	header := []byte{
		// PNG文件签名
		0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A,
		// IHDR chunk
		0x00, 0x00, 0x00, 0x0D, // chunk length: 13
		0x49, 0x48, 0x44, 0x52, // chunk type: IHDR
		0x00, 0x00, 0x00, 0x01, // width: 1
		0x00, 0x00, 0x00, 0x01, // height: 1
		0x08, 0x02, 0x00, 0x00, 0x00, // bit depth, color type, compression, filter, interlace
		0x90, 0x77, 0x53, 0xDE, // CRC
		// IDAT chunk (minimal data)
		0x00, 0x00, 0x00, 0x0C, // chunk length: 12
		0x49, 0x44, 0x41, 0x54, // chunk type: IDAT
		0x78, 0x9C, 0x62, 0x00, 0x02, 0x00, 0x00, 0x05, 0x00, 0x01, 0x0D, 0x0A, // compressed data
		0x2D, 0xB4, 0x34, 0xFB, // CRC
	}

	// 扩展到212字节（添加填充数据）
	targetSize := pd.config.PNGHeaderSize
	if targetSize < len(header) {
		targetSize = len(header)
	}

	pd.pngHeader = make([]byte, targetSize)
	copy(pd.pngHeader, header)

	// 如果需要更多字节，添加注释块
	if targetSize > len(header) {
		remaining := targetSize - len(header)
		pd.addCommentChunk(remaining)
	}

	pd.logger.Infof("✅ 简单PNG头部初始化完成: %d字节", len(pd.pngHeader))
	return nil
}

// validatePNGHeader 验证PNG头部的有效性
func (pd *pngDisguiser) validatePNGHeader() bool {
	if len(pd.pngHeader) < 8 {
		return false
	}

	// 检查PNG文件签名
	pngSignature := []byte{0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A}
	if !bytes.Equal(pd.pngHeader[:8], pngSignature) {
		pd.logger.Errorf("❌ PNG签名无效")
		return false
	}

	// 检查是否只有一个PNG签名（避免双重PNG头部问题）
	signatureCount := 0
	for i := 0; i <= len(pd.pngHeader)-8; i++ {
		if bytes.Equal(pd.pngHeader[i:i+8], pngSignature) {
			signatureCount++
		}
	}

	if signatureCount != 1 {
		pd.logger.Errorf("❌ 发现%d个PNG签名，应该只有1个", signatureCount)
		return false
	}

	// 检查IEND块
	if !bytes.Contains(pd.pngHeader, []byte("IEND")) {
		pd.logger.Errorf("❌ PNG头部缺少IEND块")
		return false
	}

	pd.logger.Infof("✅ PNG头部验证通过: %d字节，1个PNG签名，包含IEND块", len(pd.pngHeader))
	return true
}

// addCommentChunk 添加注释块来达到目标大小
func (pd *pngDisguiser) addCommentChunk(size int) {
	if size <= 8 { // 至少需要8字节用于chunk头部
		return
	}

	commentData := make([]byte, size-8)
	for i := range commentData {
		commentData[i] = 0x00 // 填充零字节
	}

	// 构建tEXt chunk
	chunkLen := len(commentData)
	chunk := make([]byte, 8+chunkLen)

	// Chunk length (4 bytes)
	chunk[0] = byte(chunkLen >> 24)
	chunk[1] = byte(chunkLen >> 16)
	chunk[2] = byte(chunkLen >> 8)
	chunk[3] = byte(chunkLen)

	// Chunk type: tEXt (4 bytes)
	copy(chunk[4:8], []byte("tEXt"))

	// Chunk data
	copy(chunk[8:], commentData)

	// 添加到头部
	pd.pngHeader = append(pd.pngHeader, chunk...)
}

// generatePNGFilename 生成PNG文件名
func (pd *pngDisguiser) generatePNGFilename(tsFilename string) string {
	// 将segment_XXX.ts转换为segment_XXX.png
	baseName := filepath.Base(tsFilename)
	ext := filepath.Ext(baseName)
	nameWithoutExt := baseName[:len(baseName)-len(ext)]
	return nameWithoutExt + ".png"
}

// disguiseWorker PNG伪装工作器
func (pd *pngDisguiser) disguiseWorker(ctx context.Context, taskChan <-chan *disguiseTask, resultChan chan<- *disguiseTaskResult) {
	for task := range taskChan {
		select {
		case <-ctx.Done():
			resultChan <- &disguiseTaskResult{
				Task:    task,
				Success: false,
				Error:   ctx.Err(),
			}
			return
		default:
		}

		// 获取信号量
		pd.semaphore <- struct{}{}

		// 执行伪装
		info, err := pd.DisguiseSegment(task.InputPath, task.OutputPath)

		// 释放信号量
		<-pd.semaphore

		result := &disguiseTaskResult{
			Task:    task,
			Success: err == nil,
			Info:    info,
			Error:   err,
		}

		resultChan <- result
	}
}

// 内部数据结构

// disguiseTask 伪装任务
type disguiseTask struct {
	Index      int
	InputPath  string
	OutputPath string
	XORKey     int
}

// disguiseTaskResult 伪装任务结果
type disguiseTaskResult struct {
	Task    *disguiseTask
	Success bool
	Info    *DisguiseInfo
	Error   error
}

package tiktok_steganography

import (
	"bufio"
	"context"
	"fmt"
	"io"
	"os"
	"os/exec"
	"path/filepath"
	"regexp"
	"sort"
	"strconv"
	"strings"
	"time"

	"github.com/sirupsen/logrus"
)

// videoSegmenter 视频切片器实现
type videoSegmenter struct {
	config *VideoProcessingConfig
	logger *logrus.Logger
}

// NewVideoSegmenter 创建新的视频切片器
func NewVideoSegmenter(config *VideoProcessingConfig, logger *logrus.Logger) (VideoSegmenter, error) {
	if config == nil {
		return nil, fmt.Errorf("config cannot be nil")
	}
	if logger == nil {
		return nil, fmt.Errorf("logger cannot be nil")
	}

	// 检查ffmpeg是否可用
	if err := checkFFmpegAvailable(); err != nil {
		return nil, fmt.Errorf("ffmpeg not available: %w", err)
	}

	return &videoSegmenter{
		config: config,
		logger: logger,
	}, nil
}

// SegmentVideo 将视频切片为TS格式
func (vs *videoSegmenter) SegmentVideo(ctx context.Context, videoPath, outputDir string) (*SegmentResult, error) {
	vs.logger.Infof("🎬 开始视频切片: %s", filepath.Base(videoPath))

	// 验证输入文件
	if err := vs.ValidateVideo(videoPath); err != nil {
		return nil, fmt.Errorf("video validation failed: %w", err)
	}

	// 创建输出目录（使用安全权限，只有所有者可访问）
	if err := os.MkdirAll(outputDir, 0700); err != nil {
		return nil, fmt.Errorf("failed to create output directory: %w", err)
	}

	// 获取视频信息
	videoInfo, err := vs.GetVideoInfo(videoPath)
	if err != nil {
		return nil, fmt.Errorf("failed to get video info: %w", err)
	}

	vs.logger.Infof("📊 视频信息: 时长=%.2fs, 大小=%d bytes, 分辨率=%dx%d",
		videoInfo.Duration, videoInfo.Size, videoInfo.Width, videoInfo.Height)

	// 估算切片数量
	expectedSegments, err := vs.EstimateSegmentCount(videoPath, vs.config.SegmentDuration)
	if err != nil {
		return nil, fmt.Errorf("failed to estimate segment count: %w", err)
	}
	vs.logger.Infof("📊 预期切片数量: %d", expectedSegments)

	// 构建ffmpeg命令
	cmd := vs.buildFFmpegCommand(videoPath, outputDir)
	vs.logger.Infof("🔧 FFmpeg命令: %s", strings.Join(cmd.Args, " "))

	// 执行切片
	startTime := time.Now()
	if err := vs.executeFFmpegCommand(ctx, cmd, outputDir); err != nil {
		return nil, fmt.Errorf("ffmpeg execution failed: %w", err)
	}
	processingTime := time.Since(startTime)

	// 扫描生成的切片文件
	segmentPaths, err := vs.scanSegmentFiles(outputDir)
	if err != nil {
		return nil, fmt.Errorf("failed to scan segment files: %w", err)
	}

	if len(segmentPaths) == 0 {
		return nil, fmt.Errorf("no segment files generated")
	}

	// 🔧 修复：生成渐进式M3U8播放列表
	if err := vs.generateProgressiveM3U8(outputDir, segmentPaths); err != nil {
		vs.logger.Warnf("⚠️ 生成渐进式M3U8失败: %v", err)
	}

	// 计算总大小
	totalSize, err := vs.calculateTotalSize(segmentPaths)
	if err != nil {
		return nil, fmt.Errorf("failed to calculate total size: %w", err)
	}

	result := &SegmentResult{
		SegmentPaths: segmentPaths,
		WorkDir:      outputDir,
		SegmentCount: len(segmentPaths),
		TotalSize:    totalSize,
		Duration:     videoInfo.Duration,
		SegmentDur:   float64(vs.config.SegmentDuration),
	}

	vs.logger.Infof("✅ 视频切片完成: %d个片段, 总大小=%d bytes, 耗时=%v",
		result.SegmentCount, result.TotalSize, processingTime)

	return result, nil
}

// GetVideoInfo 获取视频信息
func (vs *videoSegmenter) GetVideoInfo(videoPath string) (*VideoInfo, error) {
	vs.logger.Debugf("🔍 开始获取视频信息: %s", videoPath)

	// 检查文件是否存在
	fileInfo, err := os.Stat(videoPath)
	if os.IsNotExist(err) {
		vs.logger.Errorf("❌ 视频文件不存在: %s", videoPath)
		return nil, fmt.Errorf("video file does not exist: %s", videoPath)
	}
	if err != nil {
		vs.logger.Errorf("❌ 无法访问视频文件: %s, 错误: %v", videoPath, err)
		return nil, fmt.Errorf("cannot access video file: %s, error: %w", videoPath, err)
	}

	vs.logger.Debugf("✅ 视频文件存在: %s (大小: %d bytes)", videoPath, fileInfo.Size())

	// 使用ffprobe获取视频信息
	cmd := exec.Command("ffprobe",
		"-v", "quiet",
		"-print_format", "json",
		"-show_format",
		"-show_streams",
		videoPath)

	output, err := cmd.Output()
	if err != nil {
		// 获取详细的错误信息
		if exitError, ok := err.(*exec.ExitError); ok {
			vs.logger.Errorf("❌ FFprobe stderr: %s", string(exitError.Stderr))
		}
		vs.logger.Errorf("❌ FFprobe命令失败: %s", cmd.String())
		return nil, fmt.Errorf("ffprobe failed: %w", err)
	}

	vs.logger.Debugf("✅ FFprobe输出长度: %d bytes", len(output))

	// 解析ffprobe输出（简化版本，实际应该解析JSON）
	duration, err := vs.extractDuration(videoPath)
	if err != nil {
		return nil, fmt.Errorf("failed to extract duration: %w", err)
	}

	// 获取文件大小（fileInfo已在上面获取）
	if fileInfo == nil {
		fileInfo, err = os.Stat(videoPath)
		if err != nil {
			return nil, fmt.Errorf("failed to get file info: %w", err)
		}
	}

	return &VideoInfo{
		Path:     videoPath,
		Size:     fileInfo.Size(),
		Duration: duration,
		Format:   filepath.Ext(videoPath),
	}, nil
}

// ValidateVideo 验证视频文件
func (vs *videoSegmenter) ValidateVideo(videoPath string) error {
	// 检查文件是否存在
	if _, err := os.Stat(videoPath); os.IsNotExist(err) {
		return fmt.Errorf("video file not found: %s", videoPath)
	}

	// 检查文件扩展名
	ext := strings.ToLower(filepath.Ext(videoPath))
	supportedExts := []string{".mp4", ".mkv", ".avi", ".mov", ".wmv", ".flv", ".webm", ".m4v"}

	supported := false
	for _, supportedExt := range supportedExts {
		if ext == supportedExt {
			supported = true
			break
		}
	}

	if !supported {
		return fmt.Errorf("unsupported video format: %s", ext)
	}

	return nil
}

// EstimateSegmentCount 估算切片数量
func (vs *videoSegmenter) EstimateSegmentCount(videoPath string, segmentDuration int) (int, error) {
	duration, err := vs.extractDuration(videoPath)
	if err != nil {
		return 0, err
	}

	count := int((duration + float64(segmentDuration) - 1) / float64(segmentDuration))
	return count, nil
}

// calculateOptimalSegmentTime 根据文件大小计算最优分段时长
func (vs *videoSegmenter) calculateOptimalSegmentTime(fileSize int64) int {
	const (
		GB15 = 15 * 1024 * 1024 * 1024 // 15GB
		GB8  = 8 * 1024 * 1024 * 1024  // 8GB
	)

	if fileSize > GB15 {
		vs.logger.Infof("📊 大文件检测 (%.2fGB > 15GB)，使用2秒分段优化加载速度",
			float64(fileSize)/(1024*1024*1024))
		return 2 // 大文件用2秒分段
	} else if fileSize > GB8 {
		vs.logger.Infof("📊 中等文件检测 (%.2fGB > 8GB)，使用3秒分段平衡性能",
			float64(fileSize)/(1024*1024*1024))
		return 3 // 中等文件用3秒分段
	} else {
		vs.logger.Infof("📊 标准文件检测 (%.2fGB ≤ 8GB)，使用5秒分段",
			float64(fileSize)/(1024*1024*1024))
		return 5 // 小文件保持5秒分段
	}
}

// buildFFmpegCommand 构建渐进式ffmpeg命令
func (vs *videoSegmenter) buildFFmpegCommand(videoPath, outputDir string) *exec.Cmd {
	// 根据配置选择输出格式
	if vs.config.OutputFormat == "fmp4" || vs.config.EnableFMP4 {
		return vs.buildFMP4Command(videoPath, outputDir)
	}
	return vs.buildTSCommand(videoPath, outputDir)
}

// buildFMP4Command 构建fMP4格式的ffmpeg命令
func (vs *videoSegmenter) buildFMP4Command(videoPath, outputDir string) *exec.Cmd {
	segmentPath := filepath.Join(outputDir, "segment_%06d.m4s")

	vs.logger.Infof("🎬 使用fMP4格式切片策略（高性能，无转码）")

	// 获取视频信息
	videoInfo, err := vs.GetVideoInfo(videoPath)
	if err != nil {
		vs.logger.Warnf("⚠️ 无法获取视频信息，回退到固定5秒切片: %v", err)
		return vs.buildLegacyFMP4Command(videoPath, outputDir)
	}

	// 🎯 自适应分段时长：根据文件大小动态调整
	segmentTime := vs.calculateOptimalSegmentTime(videoInfo.Size)
	totalDuration := videoInfo.Duration
	estimatedSegments := int(totalDuration/float64(segmentTime)) + 1
	vs.logger.Infof("📊 视频总时长: %.2f秒, 使用%d秒分段, 预计生成 %d 个fMP4片段",
		totalDuration, segmentTime, estimatedSegments)

	args := []string{
		"-i", videoPath,
		"-c", "copy", // 无转码模式
		"-f", "hls",
		"-hls_time", fmt.Sprintf("%d", segmentTime), // 🎯 使用动态分段时长
		"-hls_segment_type", "fmp4",
		"-hls_fmp4_init_filename", "init.mp4",
		"-hls_segment_filename", segmentPath,
		"-hls_playlist_type", "vod",
		"-hls_flags", "independent_segments",
		"-y",
		filepath.Join(outputDir, "playlist.m3u8"),
	}

	return exec.Command("ffmpeg", args...)
}

// buildTSCommand 构建TS格式的ffmpeg命令（保持向后兼容）
func (vs *videoSegmenter) buildTSCommand(videoPath, outputDir string) *exec.Cmd {
	segmentPath := filepath.Join(outputDir, "segment_%06d.ts")

	// 🔧 新策略：自适应HLS切片（GOP对齐，无花屏）
	vs.logger.Infof("🎬 使用自适应HLS切片策略（GOP对齐，低延迟）")

	// 获取视频信息
	videoInfo, err := vs.GetVideoInfo(videoPath)
	if err != nil {
		vs.logger.Warnf("⚠️ 无法获取视频信息，回退到固定4秒切片: %v", err)
		return vs.buildLegacyHLSCommand(videoPath, outputDir)
	}

	// 🎯 自适应分段时长：根据文件大小动态调整
	segmentTime := vs.calculateOptimalSegmentTime(videoInfo.Size)
	totalDuration := videoInfo.Duration
	estimatedSegments := int(totalDuration/float64(segmentTime)) + 1
	vs.logger.Infof("📊 视频总时长: %.2f秒, 使用%d秒分段, 预计生成 %d 个TS片段",
		totalDuration, segmentTime, estimatedSegments)

	args := []string{
		"-i", videoPath,
	}

	// 添加自定义参数
	if vs.config.FFmpegParams != "" {
		customArgs := strings.Fields(vs.config.FFmpegParams)
		args = append(args, customArgs...)
	} else {
		// 🔧 超级优化的编码参数（充分利用6核CPU + 16GB内存）
		args = append(args,
			// 视频编码优化
			"-c:v", "libx264", // H.264编码器
			"-preset", "ultrafast", // 最快编码预设（牺牲压缩率换取速度）
			"-threads", "6", // 使用全部6个CPU核心
			"-thread_type", "frame+slice", // 帧级和片级并行
			"-slices", "6", // 6个切片并行编码

			// GOP和关键帧优化
			"-g", "125", // GOP大小：5秒×25fps=125帧
			"-keyint_min", "125", // 最小关键帧间隔
			"-sc_threshold", "0", // 禁用场景切换检测
			"-force_key_frames", "expr:gte(t,n_forced*5)", // 强制每5秒一个关键帧

			// 性能和内存优化
			"-flags", "low_delay", // 低延迟编码
			"-fflags", "+nobuffer+discardcorrupt+flush_packets", // 禁用缓冲，跳过错误包，立即刷新
			"-avioflags", "direct", // 直接磁盘I/O
			"-max_muxing_queue_size", "9999", // 增大复用队列（避免丢包）
			"-bufsize", "2M", // 2MB缓冲区
			"-maxrate", "10M", // 最大码率10Mbps

			// 音频编码优化
			"-c:a", "aac", // AAC音频编码
			"-profile:a", "aac_low", // AAC低复杂度配置
			"-ac", "2", // 双声道
			"-ar", "48000", // 48kHz采样率
			"-b:a", "128k", // 128k音频码率
			"-aac_coder", "fast", // 快速AAC编码器
		)
	}

	// 🔧 优化的HLS切片参数
	args = append(args,
		"-f", "hls", // HLS格式
		"-hls_time", fmt.Sprintf("%d", segmentTime), // 🎯 使用动态分段时长
		"-hls_list_size", "0", // 保留所有片段
		"-hls_flags", "delete_segments+split_by_time", // 删除旧片段，按时间切片
		"-hls_segment_filename", segmentPath, // 片段文件名模板
		"-hls_playlist_type", "vod", // VOD类型播放列表
		"-y", // 覆盖输出文件
		filepath.Join(outputDir, "playlist.m3u8"), // M3U8播放列表
	)

	return exec.Command("ffmpeg", args...)
}

// buildLegacyHLSCommand 构建传统HLS命令（回退方案）
func (vs *videoSegmenter) buildLegacyHLSCommand(videoPath, outputDir string) *exec.Cmd {
	segmentPath := filepath.Join(outputDir, "segment_%06d.ts")
	playlistPath := filepath.Join(outputDir, "playlist.m3u8")

	args := []string{
		"-i", videoPath,
		"-c:v", "copy",
		"-c:a", "aac", "-profile:a", "aac_low", "-ac", "2", "-ar", "48000", "-b:a", "128k",
		"-f", "hls",
		"-hls_time", fmt.Sprintf("%d", vs.config.SegmentDuration),
		"-hls_segment_filename", segmentPath,
		"-hls_list_size", "0",
		"-hls_playlist_type", "vod",
		"-force_key_frames", fmt.Sprintf("expr:gte(t,n_forced*%d)", vs.config.SegmentDuration),
		"-hls_flags", "split_by_time+round_durations",
		"-movflags", "+faststart",
		"-y",
		playlistPath,
	}

	return exec.Command("ffmpeg", args...)
}

// generateProgressiveM3U8 生成渐进式M3U8播放列表
func (vs *videoSegmenter) generateProgressiveM3U8(outputDir string, segmentPaths []string) error {
	playlistPath := filepath.Join(outputDir, "playlist.m3u8")

	// 渐进式切片时长配置：2s→4s→10s→20s
	segmentDurations := []float64{2.0, 4.0, 10.0}
	standardDuration := 20.0

	var m3u8Lines []string
	m3u8Lines = append(m3u8Lines, "#EXTM3U")
	m3u8Lines = append(m3u8Lines, "#EXT-X-VERSION:3")
	m3u8Lines = append(m3u8Lines, "#EXT-X-TARGETDURATION:20") // 最大片段时长
	m3u8Lines = append(m3u8Lines, "#EXT-X-MEDIA-SEQUENCE:0")

	vs.logger.Infof("📊 生成渐进式M3U8播放列表: %d个片段", len(segmentPaths))

	for i, segmentPath := range segmentPaths {
		// 确定当前片段的时长
		var duration float64
		if i < len(segmentDurations) {
			duration = segmentDurations[i]
		} else {
			duration = standardDuration
		}

		// 添加精确的时长标签
		m3u8Lines = append(m3u8Lines, fmt.Sprintf("#EXTINF:%.3f,", duration))
		m3u8Lines = append(m3u8Lines, filepath.Base(segmentPath))
	}

	m3u8Lines = append(m3u8Lines, "#EXT-X-ENDLIST")

	// 写入M3U8文件（使用安全权限，只有所有者可读写）
	content := strings.Join(m3u8Lines, "\n") + "\n"
	if err := os.WriteFile(playlistPath, []byte(content), 0600); err != nil {
		return fmt.Errorf("写入M3U8文件失败: %w", err)
	}

	vs.logger.Infof("✅ 渐进式M3U8播放列表已生成: %s", playlistPath)
	return nil
}

// executeFFmpegCommand 执行ffmpeg命令 - 永不停止版本
func (vs *videoSegmenter) executeFFmpegCommand(ctx context.Context, cmd *exec.Cmd, outputDir string) error {
	vs.logger.Infof("🚀 启动永不停止的FFmpeg处理...")

	// 移除超时限制 - 让FFmpeg无限运行直到完成
	// 不设置任何超时上下文，确保大文件能够完整处理

	// 创建无超时的命令
	cmd = exec.Command(cmd.Path, cmd.Args[1:]...)

	// 设置环境变量，优化FFmpeg性能
	cmd.Env = append(os.Environ(),
		"FFREPORT=file=/tmp/ffmpeg_report.log:level=32", // 详细日志
		"AV_LOG_FORCE_NOCOLOR=1",                        // 禁用颜色输出
	)

	vs.logger.Infof("🔧 FFmpeg命令: %s", strings.Join(cmd.Args, " "))

	// 启动命令并获取输出管道
	stdout, err := cmd.StdoutPipe()
	if err != nil {
		return fmt.Errorf("failed to create stdout pipe: %w", err)
	}

	stderr, err := cmd.StderrPipe()
	if err != nil {
		return fmt.Errorf("failed to create stderr pipe: %w", err)
	}

	// 启动命令
	if err := cmd.Start(); err != nil {
		return fmt.Errorf("failed to start ffmpeg: %w", err)
	}

	vs.logger.Infof("✅ FFmpeg进程已启动 (PID: %d)", cmd.Process.Pid)

	// 实时读取输出，避免缓冲区阻塞
	go vs.readOutput("STDOUT", stdout)
	go vs.readOutput("STDERR", stderr)

	// 等待命令完成 - 无超时限制
	err = cmd.Wait()

	if err != nil {
		vs.logger.Errorf("❌ FFmpeg进程异常退出: %v", err)

		// 检查是否有可用的片段文件
		segmentFiles, scanErr := vs.scanSegmentFiles(outputDir)
		if scanErr != nil || len(segmentFiles) == 0 {
			return fmt.Errorf("ffmpeg failed and no valid segments generated: %w", err)
		}

		// 验证片段文件完整性
		validSegments := 0
		for _, segFile := range segmentFiles {
			if stat, err := os.Stat(segFile); err == nil && stat.Size() > 1024 {
				validSegments++
			}
		}

		if validSegments == 0 {
			return fmt.Errorf("ffmpeg failed and no valid segments found: %w", err)
		}

		vs.logger.Warnf("⚠️ FFmpeg有错误但生成了%d个有效片段，继续处理", validSegments)
	} else {
		vs.logger.Infof("✅ FFmpeg进程正常完成")
	}

	return nil
}

// scanSegmentFiles 扫描切片文件
func (vs *videoSegmenter) scanSegmentFiles(outputDir string) ([]string, error) {
	// 根据配置选择扫描格式
	if vs.config.OutputFormat == "fmp4" || vs.config.EnableFMP4 {
		return vs.scanFMP4Files(outputDir)
	}
	return vs.scanTSFiles(outputDir)
}

// scanFMP4Files 扫描fMP4格式文件
func (vs *videoSegmenter) scanFMP4Files(outputDir string) ([]string, error) {
	files, err := os.ReadDir(outputDir)
	if err != nil {
		return nil, fmt.Errorf("failed to read output directory: %w", err)
	}

	var segmentPaths []string
	segmentPattern := regexp.MustCompile(`^segment_\d+\.m4s$`)

	// 首先添加初始化文件（如果存在）
	initPath := filepath.Join(outputDir, "init.mp4")
	if _, err := os.Stat(initPath); err == nil {
		segmentPaths = append(segmentPaths, initPath)
		vs.logger.Infof("📁 发现fMP4初始化文件: init.mp4")
	}

	// 然后添加分片文件
	for _, file := range files {
		if file.IsDir() {
			continue
		}

		if segmentPattern.MatchString(file.Name()) {
			fullPath := filepath.Join(outputDir, file.Name())
			segmentPaths = append(segmentPaths, fullPath)
		}
	}

	// 对分片文件排序（保持初始化文件在前）
	if len(segmentPaths) > 1 {
		initFile := segmentPaths[0]
		segments := segmentPaths[1:]
		sort.Strings(segments)
		segmentPaths = append([]string{initFile}, segments...)
	}

	vs.logger.Infof("📊 扫描到 %d 个fMP4文件（包含初始化文件）", len(segmentPaths))
	return segmentPaths, nil
}

// scanTSFiles 扫描TS格式文件（保持向后兼容）
func (vs *videoSegmenter) scanTSFiles(outputDir string) ([]string, error) {
	files, err := os.ReadDir(outputDir)
	if err != nil {
		return nil, fmt.Errorf("failed to read output directory: %w", err)
	}

	var segmentPaths []string
	segmentPattern := regexp.MustCompile(`^segment_\d+\.ts$`)

	for _, file := range files {
		if file.IsDir() {
			continue
		}

		if segmentPattern.MatchString(file.Name()) {
			fullPath := filepath.Join(outputDir, file.Name())
			segmentPaths = append(segmentPaths, fullPath)
		}
	}

	// 按文件名排序
	sort.Strings(segmentPaths)
	return segmentPaths, nil
}

// calculateTotalSize 计算总大小
func (vs *videoSegmenter) calculateTotalSize(segmentPaths []string) (int64, error) {
	var totalSize int64

	for _, path := range segmentPaths {
		fileInfo, err := os.Stat(path)
		if err != nil {
			return 0, fmt.Errorf("failed to get file info for %s: %w", path, err)
		}
		totalSize += fileInfo.Size()
	}

	return totalSize, nil
}

// extractDuration 提取视频时长
func (vs *videoSegmenter) extractDuration(videoPath string) (float64, error) {
	vs.logger.Debugf("🔍 提取视频时长: %s", videoPath)

	cmd := exec.Command("ffprobe",
		"-v", "quiet",
		"-show_entries", "format=duration",
		"-of", "csv=p=0",
		videoPath)

	output, err := cmd.Output()
	if err != nil {
		// 获取详细的错误信息
		if exitError, ok := err.(*exec.ExitError); ok {
			vs.logger.Errorf("❌ FFprobe duration stderr: %s", string(exitError.Stderr))
		}
		vs.logger.Errorf("❌ FFprobe duration命令失败: %s", cmd.String())
		return 0, fmt.Errorf("ffprobe duration extraction failed: %w", err)
	}

	durationStr := strings.TrimSpace(string(output))
	duration, err := strconv.ParseFloat(durationStr, 64)
	if err != nil {
		return 0, fmt.Errorf("failed to parse duration: %w", err)
	}

	return duration, nil
}

// readOutput 实时读取FFmpeg输出，避免缓冲区阻塞
func (vs *videoSegmenter) readOutput(source string, reader io.Reader) {
	scanner := bufio.NewScanner(reader)
	for scanner.Scan() {
		line := scanner.Text()
		if strings.TrimSpace(line) != "" {
			vs.logger.Debugf("[FFmpeg %s] %s", source, line)
		}
	}
	if err := scanner.Err(); err != nil {
		vs.logger.Warnf("读取FFmpeg %s输出时出错: %v", source, err)
	}
}

// buildLegacyFMP4Command 构建传统fMP4命令（回退方案）
func (vs *videoSegmenter) buildLegacyFMP4Command(videoPath, outputDir string) *exec.Cmd {
	segmentPath := filepath.Join(outputDir, "segment_%06d.m4s")

	args := []string{
		"-i", videoPath,
		"-c", "copy",
		"-f", "hls",
		"-hls_time", "5",
		"-hls_segment_type", "fmp4",
		"-hls_fmp4_init_filename", "init.mp4",
		"-hls_segment_filename", segmentPath,
		"-hls_playlist_type", "vod",
		"-hls_flags", "independent_segments",
		"-y",
		filepath.Join(outputDir, "playlist.m3u8"),
	}

	return exec.Command("ffmpeg", args...)
}

// checkFFmpegAvailable 检查ffmpeg是否可用
func checkFFmpegAvailable() error {
	cmd := exec.Command("ffmpeg", "-version")
	if err := cmd.Run(); err != nil {
		return fmt.Errorf("ffmpeg not found or not executable")
	}
	return nil
}

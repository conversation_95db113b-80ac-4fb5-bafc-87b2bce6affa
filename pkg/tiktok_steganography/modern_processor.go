package tiktok_steganography

import (
	"context"
	"fmt"
	"os"
	"path/filepath"
	"time"

	"github.com/sirupsen/logrus"
)

// ModernProcessor 现代化视频处理器
type ModernProcessor struct {
	config          *Config
	resourceMonitor *ResourceMonitor
	workerPool      *WorkerPool
	logger          *logrus.Logger

	// 组件
	videoSegmenter VideoSegmenter
	pngDisguiser   PNGDisguiser
	cdnUploader    CDNUploader

	// TikTok Manager
	tiktokManager TikTokInterfaceManager
}

// ModernProcessorConfig 现代化处理器配置
type ModernProcessorConfig struct {
	ProcessorConfig  *Config           `json:"processor_config"`
	WorkerPoolConfig *WorkerPoolConfig `json:"worker_pool_config"`
	ResourceConfig   *ResourceConfig   `json:"resource_config"`
}

// NewModernProcessor 创建现代化处理器
func NewModernProcessor(config *ModernProcessorConfig, logger *logrus.Logger) (*ModernProcessor, error) {
	if config == nil {
		return nil, fmt.Errorf("config cannot be nil")
	}
	if logger == nil {
		return nil, fmt.Errorf("logger cannot be nil")
	}

	// 使用默认配置
	if config.ProcessorConfig == nil {
		config.ProcessorConfig = DefaultConfig()
	}
	if config.WorkerPoolConfig == nil {
		config.WorkerPoolConfig = DefaultWorkerPoolConfig()
	}
	if config.ResourceConfig == nil {
		config.ResourceConfig = &ResourceConfig{
			MaxMemoryMB:     2048,
			MaxDiskSpaceMB:  10240,
			WorkDir:         "/tmp/tiktok_steganography",
			MonitorInterval: 30 * time.Second,
		}
	}

	// 创建资源监控器
	resourceMonitor := NewResourceMonitor(config.ResourceConfig, logger)

	// 创建组件（使用现有的构造函数）
	videoSegmenter, err := NewVideoSegmenter(&config.ProcessorConfig.VideoProcessing, logger)
	if err != nil {
		return nil, fmt.Errorf("failed to create video segmenter: %w", err)
	}

	pngDisguiser, err := NewPNGDisguiser(&config.ProcessorConfig.Disguise, logger)
	if err != nil {
		return nil, fmt.Errorf("failed to create PNG disguiser: %w", err)
	}

	cdnUploader, err := NewCDNUploader(&config.ProcessorConfig.Upload, logger)
	if err != nil {
		return nil, fmt.Errorf("failed to create CDN uploader: %w", err)
	}

	// 创建Worker Pool
	workerPool := NewWorkerPool(
		config.WorkerPoolConfig,
		resourceMonitor,
		videoSegmenter,
		pngDisguiser,
		cdnUploader,
		logger,
	)

	processor := &ModernProcessor{
		config:          config.ProcessorConfig,
		resourceMonitor: resourceMonitor,
		workerPool:      workerPool,
		logger:          logger,
		videoSegmenter:  videoSegmenter,
		pngDisguiser:    pngDisguiser,
		cdnUploader:     cdnUploader,
	}

	return processor, nil
}

// SetTikTokManager 设置TikTok接口管理器
func (mp *ModernProcessor) SetTikTokManager(manager TikTokInterfaceManager) {
	mp.logger.Info("🔧 设置TikTok Manager到现代化处理器")

	mp.tiktokManager = manager

	// 设置到CDN上传器
	if mp.cdnUploader != nil {
		mp.cdnUploader.SetTikTokManager(manager)
		mp.logger.Info("✅ TikTok Manager已设置到CDN上传器")
	}
}

// Start 启动处理器
func (mp *ModernProcessor) Start(ctx context.Context) error {
	mp.logger.Info("🚀 启动现代化视频处理器")

	// 启动资源监控器
	go mp.resourceMonitor.Start(ctx)

	// 启动Worker Pool
	if err := mp.workerPool.Start(); err != nil {
		return fmt.Errorf("启动Worker Pool失败: %w", err)
	}

	mp.logger.Info("✅ 现代化视频处理器启动完成")
	return nil
}

// Stop 停止处理器
func (mp *ModernProcessor) Stop() error {
	mp.logger.Info("🛑 停止现代化视频处理器")

	// 停止Worker Pool
	if err := mp.workerPool.Stop(); err != nil {
		mp.logger.Errorf("停止Worker Pool失败: %v", err)
	}

	mp.logger.Info("✅ 现代化视频处理器已停止")
	return nil
}

// ProcessVideoToCDN 处理视频到CDN（现代化版本）
func (mp *ModernProcessor) ProcessVideoToCDN(ctx context.Context, videoPath string) (*CDNResult, error) {
	mp.logger.Infof("🎬 开始现代化视频处理: %s", filepath.Base(videoPath))

	// 验证输入
	if err := mp.validateInput(videoPath); err != nil {
		return nil, err
	}

	// 创建工作目录
	workDir, err := mp.createWorkDir(videoPath)
	if err != nil {
		return nil, fmt.Errorf("创建工作目录失败: %w", err)
	}

	// 确保清理工作目录
	defer func() {
		if mp.config.Cleanup.AutoCleanup {
			if cleanupErr := mp.workerPool.CleanupWorkDir(workDir); cleanupErr != nil {
				mp.logger.Warnf("清理工作目录失败: %v", cleanupErr)
			}
		}
	}()

	// 提交任务到Worker Pool
	result, err := mp.workerPool.SubmitVideo(ctx, videoPath, workDir)
	if err != nil {
		return nil, fmt.Errorf("提交视频任务失败: %w", err)
	}

	// 转换结果
	if !result.Success {
		return nil, fmt.Errorf("视频处理失败: %w", result.Error)
	}

	// 构建CDN结果
	cdnResult := &CDNResult{
		Success:      result.Success,
		PlaylistURL:  "", // 需要根据上传结果生成
		SegmentCount: len(result.UploadResults),
		TotalSize:    0, // 需要计算
		Duration:     0, // 需要从视频信息中获取
		ProcessTime:  result.ProcessTime,
		CDNURLs:      extractCDNURLs(result.UploadResults),
		WorkDir:      workDir,
	}

	// 生成播放列表URL（简化版本）
	if len(result.UploadResults) > 0 {
		cdnResult.PlaylistURL = mp.generatePlaylistURL(result.UploadResults)
	}

	mp.logger.Infof("✅ 现代化视频处理完成: %s (耗时: %v, 片段: %d)",
		filepath.Base(videoPath), result.ProcessTime, cdnResult.SegmentCount)

	return cdnResult, nil
}

// validateInput 验证输入
func (mp *ModernProcessor) validateInput(videoPath string) error {
	// 检查文件是否存在
	fileInfo, err := os.Stat(videoPath)
	if err != nil {
		return fmt.Errorf("无法访问视频文件: %w", err)
	}

	// 检查文件大小 - 最小1GB限制
	minFileSize := int64(1024 * 1024 * 1024) // 1GB
	if fileInfo.Size() < minFileSize {
		return fmt.Errorf("视频文件太小 (%.2f GB < 1 GB): %s",
			float64(fileInfo.Size())/(1024*1024*1024), videoPath)
	}

	return nil
}

// createWorkDir 创建工作目录
func (mp *ModernProcessor) createWorkDir(videoPath string) (string, error) {
	baseDir := mp.config.WorkDir
	if baseDir == "" {
		baseDir = "/tmp/tiktok_steganography"
	}

	// 使用视频文件名和时间戳创建唯一目录
	videoName := filepath.Base(videoPath)
	timestamp := time.Now().Format("20060102_150405")
	workDir := filepath.Join(baseDir, fmt.Sprintf("%s_%s", videoName, timestamp))

	if err := os.MkdirAll(workDir, 0700); err != nil {
		return "", fmt.Errorf("创建工作目录失败: %w", err)
	}

	return workDir, nil
}

// generatePlaylistURL 生成播放列表URL（简化版本）
func (mp *ModernProcessor) generatePlaylistURL(uploadResults []*TikTokUploadResult) string {
	if len(uploadResults) == 0 {
		return ""
	}

	// 这里应该生成实际的M3U8播放列表
	// 简化版本直接返回第一个片段的URL
	return uploadResults[0].CDNURL
}

// GetStats 获取处理器统计信息
func (mp *ModernProcessor) GetStats() map[string]interface{} {
	workerStats := mp.workerPool.GetStats()
	resourceStats := mp.resourceMonitor.GetResourceStats()
	queueStatus := mp.workerPool.GetQueueStatus()

	return map[string]interface{}{
		"worker_pool": map[string]interface{}{
			"total_tasks":     workerStats.TotalTasks,
			"completed_tasks": workerStats.CompletedTasks,
			"failed_tasks":    workerStats.FailedTasks,
			"active_workers":  mp.workerPool.GetActiveWorkers(),
			"average_time":    workerStats.AverageTime.String(),
			"healthy":         mp.workerPool.IsHealthy(),
		},
		"resources": resourceStats,
		"queues":    queueStatus,
	}
}

// IsHealthy 检查处理器健康状态
func (mp *ModernProcessor) IsHealthy() bool {
	return mp.workerPool.IsHealthy()
}

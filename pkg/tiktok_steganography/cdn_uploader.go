package tiktok_steganography

import (
	"context"
	"fmt"
	"path/filepath"
	"strings"
	"sync"
	"sync/atomic"
	"time"

	"github.com/sirupsen/logrus"
)

// cdnUploader TikTok CDN上传器实现
type cdnUploader struct {
	config        *UploadConfig
	logger        *logrus.Logger
	tiktokManager TikTokInterfaceManager
	semaphore     chan struct{}
	stats         *UploadStats
	mu            sync.RWMutex

	// 失败任务队列相关
	failedTaskQueue chan *FailedTask
	retryWorkers    int
	retryContext    context.Context
	retryCancel     context.CancelFunc
	retryWg         sync.WaitGroup
}

// FailedTask 失败任务结构
type FailedTask struct {
	Task         *BatchUploadTask
	FailureCount int
	LastError    string
	LastAttempt  time.Time
	NextRetry    time.Time
}

// NewCDNUploader 创建新的CDN上传器
func NewCDNUploader(config *UploadConfig, logger *logrus.Logger) (CDNUploader, error) {
	if config == nil {
		return nil, fmt.Errorf("config cannot be nil")
	}
	if logger == nil {
		return nil, fmt.Errorf("logger cannot be nil")
	}

	// 创建重试上下文
	retryCtx, retryCancel := context.WithCancel(context.Background())

	uploader := &cdnUploader{
		config:    config,
		logger:    logger,
		semaphore: make(chan struct{}, config.ConcurrentWorkers),
		stats: &UploadStats{
			InterfaceStats: make(map[string]*InterfaceStats),
		},
		tiktokManager: nil, // 将在SetTikTokManager中设置

		// 初始化失败任务队列
		failedTaskQueue: make(chan *FailedTask, 1000), // 缓冲1000个失败任务
		retryWorkers:    2,                            // 2个重试工作器
		retryContext:    retryCtx,
		retryCancel:     retryCancel,
	}

	// 启动重试工作器
	uploader.startRetryWorkers()

	return uploader, nil
}

// SetTikTokManager 设置TikTok接口管理器
func (cu *cdnUploader) SetTikTokManager(manager TikTokInterfaceManager) {
	cu.mu.Lock()
	defer cu.mu.Unlock()

	if manager == nil {
		cu.logger.Errorf("❌ 尝试设置nil TikTok Manager到CDN上传器")
		return
	}

	cu.tiktokManager = manager
	cu.logger.Infof("✅ TikTok Manager已设置到CDN上传器")

	// 验证设置是否成功
	if cu.tiktokManager == nil {
		cu.logger.Errorf("❌ TikTok Manager设置后仍为nil")
	} else {
		cu.logger.Infof("✅ TikTok Manager设置验证成功")
	}
}

// UploadSegments 批量上传切片到TikTok CDN
func (cu *cdnUploader) UploadSegments(ctx context.Context, disguisedPaths []string) (*UploadResult, error) {
	cu.logger.Infof("📤 开始批量上传到TikTok CDN: %d个文件", len(disguisedPaths))

	if len(disguisedPaths) == 0 {
		return nil, fmt.Errorf("no disguised paths provided")
	}

	// 强制检查TikTok Manager状态
	cu.mu.RLock()
	managerAvailable := cu.tiktokManager != nil
	cu.mu.RUnlock()

	if !managerAvailable {
		cu.logger.Errorf("❌ TikTok Manager未设置到CDN上传器")
		return nil, fmt.Errorf("TikTok manager not set")
	}

	cu.logger.Infof("✅ TikTok Manager已设置，开始上传 %d 个文件", len(disguisedPaths))

	// 检测文件格式并分离初始化文件
	initFile, segmentFiles := cu.separateFilesByFormat(disguisedPaths)
	if initFile != "" {
		cu.logger.Infof("📁 检测到fMP4初始化文件: %s", filepath.Base(initFile))
	}
	cu.logger.Infof("📊 分片文件数量: %d", len(segmentFiles))

	// 在开始上传前优化并发设置
	cu.OptimizeConcurrency()

	startTime := time.Now()

	// 创建任务通道（包含初始化文件）
	totalFiles := len(segmentFiles)
	if initFile != "" {
		totalFiles++
	}
	taskChan := make(chan *BatchUploadTask, totalFiles)
	resultChan := make(chan *uploadTaskResult, totalFiles)

	// 启动worker goroutines
	cu.logger.Infof("🚀 启动 %d 个上传worker处理 %d 个文件", cu.config.ConcurrentWorkers, len(disguisedPaths))
	var wg sync.WaitGroup
	for i := 0; i < cu.config.ConcurrentWorkers; i++ {
		wg.Add(1)
		go func() {
			defer wg.Done()
			cu.uploadWorker(ctx, taskChan, resultChan)
		}()
	}

	// 🔧 修复：分离初始化文件和分片文件的Index计数
	initTaskIndex := -1 // 初始化文件使用负数索引，不影响分片序号

	// 首先发送初始化文件任务（如果存在）
	if initFile != "" {
		originalName := filepath.Base(initFile)
		filename := cu.generateUploadFilename(originalName)

		cu.logger.Infof("📝 优先处理初始化文件: %s -> %s (Index: %d)", originalName, filename, initTaskIndex)

		task := &BatchUploadTask{
			Index:      initTaskIndex,
			FilePath:   initFile,
			Filename:   filename,
			XORKey:     DefaultXORKey,
			RetryCount: 0,
			MaxRetries: cu.config.RetryAttempts,
		}

		taskChan <- task
	}

	// 然后发送分片文件任务 - 使用原始索引确保序号正确
	for _, segmentFile := range segmentFiles {
		originalName := filepath.Base(segmentFile.Path)
		filename := cu.generateUploadFilename(originalName)

		// 🔧 修复：使用OriginalIndex确保片段序号正确
		cu.logger.Infof("📝 文件名处理: %s -> %s -> %s (原始序号: %d)",
			segmentFile.Path, originalName, filename, segmentFile.OriginalIndex)

		task := &BatchUploadTask{
			Index:      segmentFile.OriginalIndex, // 🔧 使用OriginalIndex保持正确序号
			FilePath:   segmentFile.Path,
			Filename:   filename,
			XORKey:     DefaultXORKey, // 使用默认XOR密钥
			RetryCount: 0,
			MaxRetries: cu.config.RetryAttempts,
		}

		taskChan <- task
	}
	close(taskChan)

	// 等待所有worker完成
	go func() {
		wg.Wait()
		close(resultChan)
	}()

	// 🔧 修复：使用map收集结果，避免数组越界问题
	resultMap := make(map[int]*TikTokUploadResult)
	var successCount, totalCount int32
	totalFilesInt32 := int32(totalFiles)

	// 进度报告间隔
	progressInterval := totalFilesInt32 / 20 // 每5%报告一次
	if progressInterval < 10 {
		progressInterval = 10 // 至少每10个文件报告一次
	}

	for result := range resultChan {
		// 🔧 修复：使用map存储，避免负数索引和数组越界
		if result.Task.Index >= 0 {
			resultMap[result.Task.Index] = result.Result
			cu.logger.Debugf("✅ 收集结果: Index=%d, 成功=%v, URL=%s",
				result.Task.Index, result.Result.Success, result.Result.CDNURL)
		} else {
			cu.logger.Debugf("⏭️  跳过初始化文件结果: Index=%d", result.Task.Index)
		}
		atomic.AddInt32(&totalCount, 1)

		if result.Result.Success && result.Result.HasCDNURL {
			atomic.AddInt32(&successCount, 1)
		}

		// 更新统计
		cu.updateStats(result.Result)

		// 进度报告
		currentCount := atomic.LoadInt32(&totalCount)
		currentSuccess := atomic.LoadInt32(&successCount)

		if currentCount%progressInterval == 0 || currentCount == totalFilesInt32 {
			progress := float64(currentCount) / float64(totalFilesInt32) * 100
			cu.logger.Infof("📊 上传进度: %d/%d (%.1f%%), 成功: %d, 失败: %d",
				currentCount, totalFilesInt32, progress, currentSuccess, currentCount-currentSuccess)
		}

		// 记录失败的上传
		if !result.Result.Success {
			cu.logger.Warnf("❌ 上传失败: %s, 错误: %s", result.Result.Filename, result.Result.Error)
		}
	}

	uploadTime := time.Since(startTime)

	// 🔧 修复：将map转换为有序数组
	maxIndex := -1
	for index := range resultMap {
		if index > maxIndex {
			maxIndex = index
		}
	}

	results := make([]*TikTokUploadResult, maxIndex+1)
	for index, result := range resultMap {
		results[index] = result
	}

	uploadResult := &UploadResult{
		Results:      results,
		TotalCount:   int(totalCount),
		SuccessCount: int(successCount),
		FailedCount:  int(totalCount) - int(successCount),
		UploadTime:   uploadTime,
	}

	cu.logger.Infof("✅ CDN上传完成: %d/%d 成功, 耗时: %v",
		uploadResult.SuccessCount, uploadResult.TotalCount, uploadTime)

	return uploadResult, nil
}

// UploadSegment 上传单个切片
func (cu *cdnUploader) UploadSegment(ctx context.Context, filePath string, task *BatchUploadTask) (*TikTokUploadResult, error) {
	startTime := time.Now()

	// 获取健康的接口
	iface, interfaceName, err := cu.tiktokManager.GetHealthyInterface()
	if err != nil {
		return &TikTokUploadResult{
			Index:        task.Index,
			Filename:     task.Filename,
			OriginalFile: filepath.Base(filePath),
			Success:      false,
			Error:        fmt.Sprintf("no healthy interface available: %v", err),
			ResponseTime: time.Since(startTime),
		}, err
	}

	cu.logger.Infof("🔄 开始上传文件: %s -> %s (第%d个)", task.Filename, interfaceName, task.Index+1)

	// 这里应该集成现有的TikTok上传逻辑
	// 由于需要复用现有的TikTok认证，我们创建一个适配器
	result := cu.performTikTokUpload(ctx, filePath, task.Filename, iface, interfaceName)
	result.Index = task.Index
	result.OriginalFile = filepath.Base(filePath)
	result.ResponseTime = time.Since(startTime)

	// 根据结果更新接口状态
	if result.Success {
		cu.tiktokManager.MarkInterfaceHealthy(interfaceName)
	} else {
		cu.tiktokManager.MarkInterfaceUnhealthy(interfaceName, result.Error)
	}

	// 释放接口并发槽位
	cu.tiktokManager.ReleaseInterface(interfaceName)

	return result, nil
}

// GetHealthyInterfaces 获取健康的接口列表
func (cu *cdnUploader) GetHealthyInterfaces() []string {
	if cu.tiktokManager == nil {
		return []string{}
	}

	// 这里应该调用TikTok管理器的方法
	// 暂时返回空列表，实际实现时需要集成现有的接口管理
	return []string{}
}

// RefreshInterfaces 刷新接口状态
func (cu *cdnUploader) RefreshInterfaces() error {
	if cu.tiktokManager == nil {
		return fmt.Errorf("TikTok manager not set")
	}

	return cu.tiktokManager.RefreshHealthStatus()
}

// GetUploadStats 获取上传统计
func (cu *cdnUploader) GetUploadStats() *UploadStats {
	cu.mu.RLock()
	defer cu.mu.RUnlock()

	// 返回统计副本
	stats := &UploadStats{
		TotalUploads:   cu.stats.TotalUploads,
		SuccessUploads: cu.stats.SuccessUploads,
		FailedUploads:  cu.stats.FailedUploads,
		TotalSize:      cu.stats.TotalSize,
		AverageTime:    cu.stats.AverageTime,
		TotalTime:      cu.stats.TotalTime,
		InterfaceStats: make(map[string]*InterfaceStats),
	}

	for name, ifaceStats := range cu.stats.InterfaceStats {
		stats.InterfaceStats[name] = &InterfaceStats{
			Name:        ifaceStats.Name,
			Uploads:     ifaceStats.Uploads,
			Successes:   ifaceStats.Successes,
			Failures:    ifaceStats.Failures,
			AverageTime: ifaceStats.AverageTime,
			LastUsed:    ifaceStats.LastUsed,
			ErrorRate:   ifaceStats.ErrorRate,
		}
	}

	return stats
}

// performTikTokUpload 执行TikTok上传（真实实现）
func (cu *cdnUploader) performTikTokUpload(ctx context.Context, filePath, filename string, iface *TikTokInterface, interfaceName string) *TikTokUploadResult {
	cu.logger.Debugf("🚀 开始真实上传: %s -> %s", filename, interfaceName)

	// 使用TikTok管理器进行真实上传
	if cu.tiktokManager == nil {
		return &TikTokUploadResult{
			Filename:  filename,
			Success:   false,
			Interface: interfaceName,
			Error:     "TikTok manager not set",
		}
	}

	// 调用真实的TikTok上传API
	uploadResult, err := cu.tiktokManager.UploadFile(ctx, filePath, filename, iface)
	if err != nil {
		cu.logger.Errorf("TikTok上传失败 %s: %v", filename, err)
		return &TikTokUploadResult{
			Filename:  filename,
			Success:   false,
			Interface: interfaceName,
			Error:     err.Error(),
		}
	}

	// 转换结果格式
	result := &TikTokUploadResult{
		Filename:     filename,
		Success:      uploadResult.Success,
		CDNURL:       uploadResult.CDNURL,
		URI:          uploadResult.URI,
		FileID:       uploadResult.FileID,
		HasCDNURL:    uploadResult.HasCDNURL,
		Interface:    interfaceName,
		ResponseTime: uploadResult.ResponseTime,
		Size:         uploadResult.Size,
		Error:        uploadResult.Error,
	}

	if result.Success {
		cu.logger.Debugf("✅ 上传成功: %s -> %s", filename, result.CDNURL)
	} else {
		cu.logger.Warnf("❌ 上传失败: %s, 错误: %s", filename, result.Error)
	}

	return result
}

// generateUploadFilename 生成上传文件名（保持唯一性）
func (cu *cdnUploader) generateUploadFilename(originalFilename string) string {
	// 保持现有的文件命名规则：'getav.net@xxx.[extension]'
	baseName := filepath.Base(originalFilename)
	ext := filepath.Ext(baseName)
	nameWithoutExt := baseName[:len(baseName)-len(ext)]

	// 如果还没有getav.net前缀，添加它（保持原始文件名的唯一性）
	if !contains(nameWithoutExt, "getav.net@") {
		return fmt.Sprintf("getav.net@%s%s", nameWithoutExt, ext)
	}

	// 如果已经有前缀，直接返回baseName（不是完整路径）
	return baseName
}

// uploadWorker 上传工作器
func (cu *cdnUploader) uploadWorker(ctx context.Context, taskChan <-chan *BatchUploadTask, resultChan chan<- *uploadTaskResult) {
	cu.logger.Debugf("🔧 Worker启动，等待任务...")
	for task := range taskChan {
		cu.logger.Infof("📥 Worker收到任务: %s (索引: %d)", task.Filename, task.Index)
		select {
		case <-ctx.Done():
			resultChan <- &uploadTaskResult{
				Task: task,
				Result: &TikTokUploadResult{
					Index:        task.Index,
					Filename:     task.Filename,
					OriginalFile: filepath.Base(task.FilePath),
					Success:      false,
					Error:        "context cancelled",
				},
			}
			return
		default:
		}

		// 获取信号量
		cu.semaphore <- struct{}{}

		// 确保信号量总是被释放，即使发生panic
		defer func() {
			<-cu.semaphore
		}()

		// 执行上传（带重试）
		result := cu.uploadWithRetry(ctx, task)

		resultChan <- &uploadTaskResult{
			Task:   task,
			Result: result,
		}
	}
}

// uploadWithRetry 带重试的上传
func (cu *cdnUploader) uploadWithRetry(ctx context.Context, task *BatchUploadTask) *TikTokUploadResult {
	var lastResult *TikTokUploadResult

	for attempt := 0; attempt <= task.MaxRetries; attempt++ {
		if attempt > 0 {
			// 重试延迟
			select {
			case <-ctx.Done():
				return &TikTokUploadResult{
					Index:        task.Index,
					Filename:     task.Filename,
					OriginalFile: filepath.Base(task.FilePath),
					Success:      false,
					Error:        "context cancelled during retry",
				}
			case <-time.After(cu.config.RetryDelay):
			}

			cu.logger.Debugf("🔄 重试上传 %s (尝试 %d/%d)", task.Filename, attempt+1, task.MaxRetries+1)
		}

		result, err := cu.UploadSegment(ctx, task.FilePath, task)
		if err == nil && result.Success && result.HasCDNURL {
			return result
		}

		lastResult = result
		if err != nil && result != nil {
			result.Error = err.Error()
		}
	}

	// 所有重试都失败了
	if lastResult == nil {
		lastResult = &TikTokUploadResult{
			Index:        task.Index,
			Filename:     task.Filename,
			OriginalFile: filepath.Base(task.FilePath),
			Success:      false,
			Error:        "all retry attempts failed",
		}
	}

	// 将失败的任务加入智能重试队列
	cu.addToFailedQueue(task, lastResult.Error)

	return lastResult
}

// updateStats 更新统计信息
func (cu *cdnUploader) updateStats(result *TikTokUploadResult) {
	cu.mu.Lock()
	defer cu.mu.Unlock()

	cu.stats.TotalUploads++
	if result.Success {
		cu.stats.SuccessUploads++
	} else {
		cu.stats.FailedUploads++
	}

	cu.stats.TotalTime += result.ResponseTime
	if cu.stats.TotalUploads > 0 {
		cu.stats.AverageTime = cu.stats.TotalTime / time.Duration(cu.stats.TotalUploads)
	}

	// 更新接口统计
	if result.Interface != "" {
		ifaceStats, exists := cu.stats.InterfaceStats[result.Interface]
		if !exists {
			ifaceStats = &InterfaceStats{
				Name: result.Interface,
			}
			cu.stats.InterfaceStats[result.Interface] = ifaceStats
		}

		ifaceStats.Uploads++
		if result.Success {
			ifaceStats.Successes++
		} else {
			ifaceStats.Failures++
		}
		ifaceStats.LastUsed = time.Now()
		if ifaceStats.Uploads > 0 {
			ifaceStats.ErrorRate = float64(ifaceStats.Failures) / float64(ifaceStats.Uploads)
		}
	}
}

// 内部数据结构

// uploadTaskResult 上传任务结果
type uploadTaskResult struct {
	Task   *BatchUploadTask
	Result *TikTokUploadResult
}

// 辅助函数
func contains(s, substr string) bool {
	return len(s) >= len(substr) && s[:len(substr)] == substr ||
		len(s) > len(substr) && s[len(s)-len(substr):] == substr ||
		(len(s) > len(substr) && len(substr) > 0 &&
			len(s) >= len(substr) &&
			func() bool {
				for i := 0; i <= len(s)-len(substr); i++ {
					if s[i:i+len(substr)] == substr {
						return true
					}
				}
				return false
			}())
}

// startRetryWorkers 启动重试工作器
func (cu *cdnUploader) startRetryWorkers() {
	for i := 0; i < cu.retryWorkers; i++ {
		cu.retryWg.Add(1)
		go cu.retryWorker(i)
	}
	cu.logger.Infof("🔄 启动了 %d 个智能重试工作器", cu.retryWorkers)
}

// retryWorker 重试工作器
func (cu *cdnUploader) retryWorker(workerID int) {
	defer cu.retryWg.Done()

	cu.logger.Debugf("🔧 重试工作器 #%d 启动", workerID)

	for {
		select {
		case <-cu.retryContext.Done():
			cu.logger.Debugf("🔧 重试工作器 #%d 停止", workerID)
			return
		case failedTask := <-cu.failedTaskQueue:
			cu.processFailedTask(workerID, failedTask)
		}
	}
}

// addToFailedQueue 将失败任务加入队列
func (cu *cdnUploader) addToFailedQueue(task *BatchUploadTask, errorMsg string) {
	failedTask := &FailedTask{
		Task:         task,
		FailureCount: 1,
		LastError:    errorMsg,
		LastAttempt:  time.Now(),
		NextRetry:    time.Now().Add(cu.calculateRetryDelay(1)),
	}

	select {
	case cu.failedTaskQueue <- failedTask:
		cu.logger.Debugf("📥 任务加入重试队列: %s (错误: %s)", task.Filename, errorMsg)
	default:
		cu.logger.Warnf("⚠️  重试队列已满，丢弃失败任务: %s", task.Filename)
	}
}

// processFailedTask 处理失败任务
func (cu *cdnUploader) processFailedTask(workerID int, failedTask *FailedTask) {
	// 检查是否到了重试时间
	if time.Now().Before(failedTask.NextRetry) {
		// 还没到重试时间，重新放回队列
		time.Sleep(time.Until(failedTask.NextRetry))
	}

	cu.logger.Debugf("🔄 工作器 #%d 开始重试任务: %s (第%d次重试)",
		workerID, failedTask.Task.Filename, failedTask.FailureCount)

	// 使用智能接口选择重新尝试上传
	ctx := context.Background()
	result, err := cu.UploadSegmentWithSmartRetry(ctx, failedTask.Task.FilePath, failedTask.Task)

	if err == nil && result.Success && result.HasCDNURL {
		cu.logger.Infof("✅ 智能重试成功: %s (工作器 #%d)", failedTask.Task.Filename, workerID)
		return
	}

	// 重试仍然失败
	failedTask.FailureCount++
	failedTask.LastError = result.Error
	failedTask.LastAttempt = time.Now()

	// 检查是否超过最大重试次数
	maxSmartRetries := 5 // 智能重试最多5次
	if failedTask.FailureCount >= maxSmartRetries {
		cu.logger.Errorf("❌ 智能重试最终失败: %s (重试%d次)",
			failedTask.Task.Filename, failedTask.FailureCount)
		return
	}

	// 计算下次重试时间（指数退避）
	failedTask.NextRetry = time.Now().Add(cu.calculateRetryDelay(failedTask.FailureCount))

	// 重新加入队列
	select {
	case cu.failedTaskQueue <- failedTask:
		cu.logger.Debugf("📥 任务重新加入重试队列: %s (第%d次重试)",
			failedTask.Task.Filename, failedTask.FailureCount)
	default:
		cu.logger.Warnf("⚠️  重试队列已满，放弃任务: %s", failedTask.Task.Filename)
	}
}

// UploadSegmentWithSmartRetry 使用智能重试的上传方法
func (cu *cdnUploader) UploadSegmentWithSmartRetry(ctx context.Context, filePath string, task *BatchUploadTask) (*TikTokUploadResult, error) {
	// 尝试多个不同的接口，而不是重复使用同一个接口
	maxInterfaceAttempts := 3

	for attempt := 0; attempt < maxInterfaceAttempts; attempt++ {
		// 获取健康的接口（智能选择会自动选择最佳接口）
		iface, interfaceName, err := cu.tiktokManager.GetHealthyInterface()
		if err != nil {
			cu.logger.Warnf("🔄 智能重试获取接口失败 (尝试 %d/%d): %v",
				attempt+1, maxInterfaceAttempts, err)

			// 如果获取接口失败，等待一段时间后重试
			if attempt < maxInterfaceAttempts-1 {
				time.Sleep(time.Duration(attempt+1) * time.Second)
				continue
			}

			return &TikTokUploadResult{
				Index:        task.Index,
				Filename:     task.Filename,
				OriginalFile: filepath.Base(filePath),
				Success:      false,
				Error:        fmt.Sprintf("no healthy interface available after %d attempts: %v", maxInterfaceAttempts, err),
			}, err
		}

		cu.logger.Debugf("🎯 智能重试使用接口: %s (尝试 %d/%d)", interfaceName, attempt+1, maxInterfaceAttempts)

		// 执行上传
		result := cu.performTikTokUpload(ctx, filePath, task.Filename, iface, interfaceName)
		result.Index = task.Index
		result.OriginalFile = filepath.Base(filePath)

		// 根据结果更新接口状态
		if result.Success {
			cu.tiktokManager.MarkInterfaceHealthy(interfaceName)
		} else {
			cu.tiktokManager.MarkInterfaceUnhealthy(interfaceName, result.Error)
		}

		// 释放接口并发槽位
		cu.tiktokManager.ReleaseInterface(interfaceName)

		// 如果成功，直接返回
		if result.Success && result.HasCDNURL {
			return result, nil
		}

		// 如果失败，记录错误并尝试下一个接口
		cu.logger.Debugf("🔄 智能重试失败 (尝试 %d/%d): %s -> %s",
			attempt+1, maxInterfaceAttempts, interfaceName, result.Error)
	}

	// 所有接口都尝试失败
	return &TikTokUploadResult{
		Index:        task.Index,
		Filename:     task.Filename,
		OriginalFile: filepath.Base(filePath),
		Success:      false,
		Error:        fmt.Sprintf("all %d interface attempts failed", maxInterfaceAttempts),
	}, fmt.Errorf("all interface attempts failed")
}

// calculateRetryDelay 计算重试延迟（指数退避）
func (cu *cdnUploader) calculateRetryDelay(failureCount int) time.Duration {
	// 基础延迟：2秒
	baseDelay := 2 * time.Second

	// 指数退避：2^failureCount * baseDelay，最大不超过60秒
	delay := time.Duration(1<<uint(failureCount)) * baseDelay
	maxDelay := 60 * time.Second

	if delay > maxDelay {
		delay = maxDelay
	}

	cu.logger.Debugf("📊 计算重试延迟: 失败次数=%d, 延迟=%v", failureCount, delay)
	return delay
}

// Shutdown 关闭CDN上传器
func (cu *cdnUploader) Shutdown() {
	cu.logger.Info("🔄 正在关闭CDN上传器...")

	// 取消重试上下文
	if cu.retryCancel != nil {
		cu.retryCancel()
	}

	// 等待所有重试工作器完成
	cu.retryWg.Wait()

	// 关闭失败任务队列
	close(cu.failedTaskQueue)

	cu.logger.Info("✅ CDN上传器已关闭")
}

// GetLoadBalanceInfo 获取负载均衡信息
func (cu *cdnUploader) GetLoadBalanceInfo() map[string]interface{} {
	cu.mu.RLock()
	defer cu.mu.RUnlock()

	info := make(map[string]interface{})

	if cu.tiktokManager != nil {
		// 获取TikTok管理器的负载统计
		managerStats := cu.tiktokManager.GetLoadBalanceStats()
		info["tiktok_manager"] = managerStats
	}

	// 添加CDN上传器自身的统计
	info["cdn_uploader"] = map[string]interface{}{
		"total_uploads":     cu.stats.TotalUploads,
		"success_uploads":   cu.stats.SuccessUploads,
		"failed_uploads":    cu.stats.FailedUploads,
		"success_rate":      cu.calculateSuccessRate(),
		"average_time_ms":   cu.stats.AverageTime.Milliseconds(),
		"failed_queue_size": len(cu.failedTaskQueue),
		"retry_workers":     cu.retryWorkers,
	}

	return info
}

// calculateSuccessRate 计算成功率
func (cu *cdnUploader) calculateSuccessRate() float64 {
	if cu.stats.TotalUploads == 0 {
		return 0.0
	}
	return float64(cu.stats.SuccessUploads) / float64(cu.stats.TotalUploads) * 100
}

// OptimizeConcurrency 优化并发设置
func (cu *cdnUploader) OptimizeConcurrency() {
	if cu.tiktokManager == nil {
		return
	}

	loadStats := cu.tiktokManager.GetLoadBalanceStats()

	// 获取当前负载信息
	totalConcurrent, _ := loadStats["total_current_concurrent"].(int)
	maxTotalConcurrent, _ := loadStats["max_total_concurrent"].(int)
	healthyInterfaces, _ := loadStats["healthy_interfaces"].(int)

	if maxTotalConcurrent == 0 || healthyInterfaces == 0 {
		return
	}

	// 计算当前负载率
	loadRatio := float64(totalConcurrent) / float64(maxTotalConcurrent)

	cu.logger.Infof("📊 当前负载状态: %d/%d (%.1f%%), 健康接口: %d",
		totalConcurrent, maxTotalConcurrent, loadRatio*100, healthyInterfaces)

	// 如果负载过高（>90%），记录警告
	if loadRatio > 0.9 {
		cu.logger.Warnf("⚠️  系统负载过高 (%.1f%%), 建议增加接口或提高并发限制", loadRatio*100)
	}

	// 如果负载很低（<30%），建议优化
	if loadRatio < 0.3 && cu.stats.TotalUploads > 100 {
		cu.logger.Infof("💡 系统负载较低 (%.1f%%), 可以考虑增加并发数以提高效率", loadRatio*100)
	}
}

// SegmentFileInfo 分片文件信息
type SegmentFileInfo struct {
	Path          string
	OriginalIndex int
}

// separateFilesByFormat 根据文件格式分离初始化文件和分片文件
func (cu *cdnUploader) separateFilesByFormat(disguisedPaths []string) (string, []SegmentFileInfo) {
	var initFile string
	var segmentFiles []SegmentFileInfo

	for i, path := range disguisedPaths {
		// 🔧 修复：跳过空字符串（伪装失败的文件）
		if path == "" {
			cu.logger.Warnf("⏭️  跳过空路径: Index=%d (伪装失败)", i)
			continue
		}

		fileName := filepath.Base(path)

		// 检查是否为初始化文件
		if strings.Contains(fileName, "init.mp4") || strings.Contains(path, "init.mp4") {
			initFile = path
			cu.logger.Infof("📁 发现fMP4初始化文件: %s", fileName)
		} else {
			segmentFiles = append(segmentFiles, SegmentFileInfo{
				Path:          path,
				OriginalIndex: i,
			})
			cu.logger.Debugf("📄 添加分片文件: OriginalIndex=%d, %s", i, fileName)
		}
	}

	return initFile, segmentFiles
}

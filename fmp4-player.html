<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🚀 fMP4智能播放器</title>
    <script src="https://cdn.jsdelivr.net/npm/hls.js@latest"></script>
    <script src="https://cdn.jsdelivr.net/npm/plyr@3/dist/plyr.min.js"></script>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/plyr@3/dist/plyr.css" />
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
        }
        
        .video-container {
            margin: 20px 0;
            border-radius: 12px;
            overflow: hidden;
            background: #000;
            box-shadow: 0 4px 20px rgba(0,0,0,0.3);
            position: relative;
        }
        
        video {
            width: 100%;
            height: auto;
            display: block;
        }
        
        .controls {
            display: grid;
            grid-template-columns: 1fr auto auto;
            gap: 15px;
            margin: 20px 0;
            align-items: center;
        }
        
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            background: #007bff;
            color: white;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s;
        }
        
        .btn:hover {
            background: #0056b3;
            transform: translateY(-1px);
        }
        
        .btn.success { background: #28a745; }
        .btn.success:hover { background: #218838; }
        
        input[type="text"] {
            padding: 12px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 14px;
        }
        
        .status {
            padding: 12px 20px;
            border-radius: 8px;
            margin: 15px 0;
            font-weight: 500;
        }
        
        .status.success { background: #d4edda; color: #155724; }
        .status.error { background: #f8d7da; color: #721c24; }
        .status.info { background: #d1ecf1; color: #0c5460; }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        
        .stat-card {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
            border-left: 4px solid #007bff;
        }
        
        .stat-value {
            font-size: 24px;
            font-weight: bold;
            color: #007bff;
        }
        
        .stat-label {
            font-size: 12px;
            color: #6c757d;
            margin-top: 5px;
        }
        
        .format-indicator {
            position: absolute;
            top: 10px;
            right: 10px;
            background: rgba(0, 123, 255, 0.9);
            color: white;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: bold;
        }
        
        .format-indicator.fmp4 {
            background: rgba(40, 167, 69, 0.9);
        }

        .format-indicator.ts {
            background: rgba(255, 152, 0, 0.9);
        }
        
        .debug-log {
            background: #2d3748;
            color: #e2e8f0;
            border-radius: 8px;
            padding: 15px;
            margin: 20px 0;
            font-family: 'Monaco', 'Menlo', monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
            display: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 fMP4智能播放器</h1>
        <p>支持fMP4格式的PNG伪装解码播放，性能更优，兼容性更好</p>
        
        <div class="controls">
            <input type="text" id="playlistUrl" placeholder="输入fMP4 M3U8播放列表URL" 
                   value="https://plyr.16bb.de/data/playlists/playlist_20250724_132652.m3u8">
            <button class="btn" onclick="loadVideo()">🎥 加载视频</button>
            <button class="btn success" onclick="testPlayback()">🧪 播放测试</button>
        </div>
        
        <div class="controls">
            <label style="display: flex; align-items: center; gap: 10px;">
                <input type="checkbox" id="enableDebug" onchange="toggleDebug()">
                <span>🔍 调试模式</span>
            </label>
            <button class="btn" onclick="showStats()">📊 性能统计</button>
            <button class="btn" onclick="clearVideo()">🗑️ 清除视频</button>
        </div>
        
        <div id="status" class="status info">准备加载fMP4视频...</div>
        
        <div class="video-container">
            <video id="player" controls crossorigin playsinline webkit-playsinline>
                <source src="" type="application/x-mpegURL">
            </video>
            <div id="formatIndicator" class="format-indicator" style="display: none;">fMP4</div>
        </div>
        
        <div id="statsGrid" class="stats-grid" style="display: none;">
            <div class="stat-card">
                <div class="stat-value" id="loadTime">--</div>
                <div class="stat-label">加载时间 (ms)</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="segmentCount">0</div>
                <div class="stat-label">片段数量</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="errorCount">0</div>
                <div class="stat-label">错误次数</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="bufferHealth">--</div>
                <div class="stat-label">缓冲健康度</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="formatType">--</div>
                <div class="stat-label">片段格式</div>
            </div>
        </div>
        
        <div id="debugLog" class="debug-log"></div>
        
        <div style="margin: 30px 0;">
            <h3>🎯 fMP4优势:</h3>
            <ul>
                <li>✅ <strong>原生支持</strong>: 浏览器硬件加速解码</li>
                <li>✅ <strong>更高性能</strong>: CPU使用率降低30-50%</li>
                <li>✅ <strong>更好兼容性</strong>: iOS Safari完美支持</li>
                <li>✅ <strong>更小文件</strong>: 比TS格式小15-25%</li>
                <li>✅ <strong>更快加载</strong>: 减少解码开销</li>
                <li>✅ <strong>PNG伪装</strong>: 保持隐写术保护</li>
            </ul>
        </div>
    </div>
    
    <script>
        let player;
        let hls;
        let debugMode = false;
        let startTime;
        let errorCount = 0;
        let segmentCount = 0;
        let isMP4Format = false;
        
        // 🎯 fMP4 PNG解码器
        class FMP4SteganographyLoader extends Hls.DefaultConfig.loader {
            constructor(config) {
                super(config);
                this.cache = new Map();
                this.retryCount = new Map();
                this.maxCacheSize = 100;
            }
            
            load(context, config, callbacks) {
                const url = context.url;

                // 区分M3U8播放列表和片段文件
                if (url.includes('.m3u8') || context.type === 'manifest') {
                    // 播放列表文件，使用默认加载器
                    super.load(context, config, callbacks);
                    return;
                }

                // 只对TikTok CDN的片段文件进行PNG解码
                if (!url.includes('ibyteimg.com') && !url.includes('tiktok')) {
                    super.load(context, config, callbacks);
                    return;
                }

                // 检查缓存
                if (this.cache.has(url)) {
                    debugLog(`🎯 缓存命中: ${url.substring(url.length - 20)}`);
                    const cachedData = this.cache.get(url);
                    const response = {
                        url: url,
                        data: cachedData,
                        code: 200,
                        text: 'OK'
                    };
                    callbacks.onSuccess(response, {}, context);
                    return;
                }

                debugLog(`📥 加载fMP4片段: ${url.substring(url.length - 20)}`);

                // 下载PNG伪装文件
                fetch(url, {
                    method: 'GET',
                    cache: 'default',
                    credentials: 'omit'
                })
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP ${response.status}`);
                    }
                    return response.arrayBuffer();
                })
                .then(arrayBuffer => {
                    const data = new Uint8Array(arrayBuffer);
                    debugLog(`📦 下载完成: ${data.length} bytes`);
                    
                    // PNG伪装解码
                    const result = this.decodePNGDisguise(data, url);
                    
                    // 智能缓存管理
                    if (this.cache.size >= this.maxCacheSize) {
                        const firstKey = this.cache.keys().next().value;
                        this.cache.delete(firstKey);
                    }
                    
                    this.cache.set(url, result);
                    segmentCount++;
                    updateStats();

                    // HLS.js需要特定的响应格式
                    const response = {
                        url: url,
                        data: result,
                        code: 200,
                        text: 'OK'
                    };

                    callbacks.onSuccess(response, {}, context);
                })
                .catch(error => {
                    debugLog(`❌ 加载失败: ${error.message}`);
                    errorCount++;
                    updateStats();
                    
                    // 重试逻辑
                    const retries = this.retryCount.get(url) || 0;
                    if (retries < 3) {
                        this.retryCount.set(url, retries + 1);
                        debugLog(`🔄 重试 ${retries + 1}/3: ${url.substring(url.length - 20)}`);
                        setTimeout(() => {
                            this.load(context, config, callbacks);
                        }, 1000 * (retries + 1));
                    } else {
                        callbacks.onError({
                            type: 'network',
                            details: 'fmp4-load-error',
                            fatal: false,
                            url: url,
                            response: { code: 0, text: error.message }
                        }, context);
                    }
                });
            }
            
            // 解码PNG伪装的fMP4数据
            decodePNGDisguise(data, url) {
                debugLog(`🎭 解码PNG伪装: ${url.substring(url.length - 20)}`);

                // 检测PNG头部大小，优先检查70字节（TikTok原版）
                let headerSize = 70; // 默认70字节
                const testSizes = [70, 212, 544894];
                let foundValidHeader = false;

                for (const size of testSizes) {
                    if (data.length > size + 100) {
                        const testData = data.slice(size);

                        // 检查fMP4文件头的多种可能性
                        const byte0 = testData[0] ^ 217;
                        const byte4 = testData[4] ^ 217;
                        const byte5 = testData[5] ^ 217;
                        const byte6 = testData[6] ^ 217;
                        const byte7 = testData[7] ^ 217;

                        // fMP4文件头检查 (ftyp box)
                        if (byte0 === 0x00 && byte4 === 0x66 && byte5 === 0x74 && byte6 === 0x79 && byte7 === 0x70) {
                            headerSize = size;
                            foundValidHeader = true;
                            debugLog(`✅ 检测到fMP4头部，偏移: ${size}字节`);
                            break;
                        }

                        // 也检查其他可能的fMP4头部模式
                        if (testData.length >= 12) {
                            const boxType = String.fromCharCode(byte4, byte5, byte6, byte7);
                            if (boxType === 'ftyp' || boxType === 'styp' || boxType === 'moof') {
                                headerSize = size;
                                foundValidHeader = true;
                                debugLog(`✅ 检测到fMP4 ${boxType} box，偏移: ${size}字节`);
                                break;
                            }
                        }
                    }
                }

                if (!foundValidHeader) {
                    debugLog(`⚠️ 未找到有效的fMP4头部，使用默认偏移: ${headerSize}字节`);
                }

                // XOR解密
                const encrypted = data.slice(headerSize);
                const decrypted = new Uint8Array(encrypted.length);
                for (let i = 0; i < encrypted.length; i++) {
                    decrypted[i] = encrypted[i] ^ 217;
                }

                // 验证解码结果
                if (decrypted.length >= 12) {
                    const boxSize = (decrypted[0] << 24) | (decrypted[1] << 16) | (decrypted[2] << 8) | decrypted[3];
                    const boxType = String.fromCharCode(decrypted[4], decrypted[5], decrypted[6], decrypted[7]);
                    debugLog(`📦 解码验证: 大小=${boxSize}, 类型=${boxType}`);

                    if (boxType !== 'ftyp' && boxType !== 'styp' && boxType !== 'moof' && boxType !== 'mdat') {
                        debugLog(`❌ 警告: 解码后的box类型异常: ${boxType}`);
                    }
                }

                debugLog(`🔓 解密完成: ${decrypted.length} bytes`);
                return decrypted.buffer;
            }
        }
        
        // 智能检测播放列表格式
        async function detectPlaylistFormat(url) {
            try {
                const response = await fetch(url);
                const content = await response.text();

                // 检查是否包含fMP4特征
                const hasFMP4Init = content.includes('#EXT-X-MAP:');
                const hasVersion7 = content.includes('#EXT-X-VERSION:7');

                isMP4Format = hasFMP4Init && hasVersion7;
                document.getElementById('formatType').textContent = isMP4Format ? 'fMP4' : 'TS';

                debugLog(`📋 格式检测: ${isMP4Format ? 'fMP4' : 'TS'} (初始化段: ${hasFMP4Init}, 版本7: ${hasVersion7})`);

            } catch (error) {
                debugLog(`⚠️ 格式检测失败，使用URL判断: ${error.message}`);
                // 回退到URL检测
                isMP4Format = url.includes('fmp4') || url.includes('.m4s');
                document.getElementById('formatType').textContent = isMP4Format ? 'fMP4' : 'TS';
            }
        }

        // 加载视频
        async function loadVideo() {
            const url = document.getElementById('playlistUrl').value.trim();
            if (!url) {
                showStatus('请输入M3U8播放列表URL', 'error');
                return;
            }
            
            clearVideo();
            startTime = Date.now();
            
            showStatus('🚀 开始加载fMP4视频...', 'info');
            debugLog('开始加载视频: ' + url);
            
            // 智能检测格式 - 通过分析M3U8内容
            await detectPlaylistFormat(url);

            const formatIndicator = document.getElementById('formatIndicator');
            if (isMP4Format) {
                formatIndicator.textContent = 'fMP4';
                formatIndicator.className = 'format-indicator fmp4';
                formatIndicator.style.display = 'block';
            } else {
                formatIndicator.textContent = 'TS';
                formatIndicator.className = 'format-indicator ts';
                formatIndicator.style.display = 'block';
            }
            
            // 初始化播放器
            const video = document.getElementById('player');
            player = new Plyr(video, {
                controls: ['play-large', 'play', 'progress', 'current-time', 'duration', 'mute', 'volume', 'fullscreen'],
                settings: ['quality', 'speed'],
                quality: { default: 'auto' }
            });
            
            // 检查HLS支持
            if (Hls.isSupported()) {
                hls = new Hls({
                    debug: debugMode,
                    loader: FMP4SteganographyLoader,
                    enableWorker: true,

                    // fMP4特定配置
                    fmp4: true,

                    // 缓冲配置
                    maxBufferLength: 60,
                    maxMaxBufferLength: 120,
                    maxBufferSize: 60 * 1000 * 1000,
                    maxBufferHole: 2.0,
                    progressive: true,

                    // 错误恢复配置
                    fragLoadingMaxRetry: 5,
                    manifestLoadingMaxRetry: 5,
                    levelLoadingMaxRetry: 5,
                    appendErrorMaxRetry: 5,

                    // 降低错误敏感度
                    fragLoadingTimeOut: 20000,
                    manifestLoadingTimeOut: 10000
                });
                
                hls.loadSource(url);
                hls.attachMedia(video);
                
                // 事件监听
                hls.on(Hls.Events.MANIFEST_PARSED, (event, data) => {
                    const loadTime = Date.now() - startTime;
                    debugLog(`播放列表解析完成，用时: ${loadTime}ms`);
                    showStatus(`✅ fMP4播放列表加载成功 (${loadTime}ms)`, 'success');
                    
                    document.getElementById('loadTime').textContent = loadTime;
                    document.getElementById('statsGrid').style.display = 'grid';
                });
                
                hls.on(Hls.Events.FRAG_LOADED, () => {
                    updateStats();
                });
                
                hls.on(Hls.Events.ERROR, (event, data) => {
                    errorCount++;
                    updateStats();
                    debugLog(`HLS错误: ${data.type} - ${data.details}`);
                    
                    if (data.fatal) {
                        showStatus(`❌ 致命错误: ${data.details}`, 'error');
                    } else if (debugMode) {
                        showStatus(`⚠️ 非致命错误: ${data.details}`, 'info');
                    }
                });
                
            } else if (video.canPlayType('application/vnd.apple.mpegurl')) {
                // Safari原生HLS支持
                video.src = url;
                showStatus('✅ 使用Safari原生HLS播放', 'success');
            } else {
                showStatus('❌ 浏览器不支持HLS播放', 'error');
            }
            
            // 播放器事件
            player.on('play', () => {
                debugLog('开始播放');
                showStatus('▶️ fMP4视频播放中', 'success');
            });
            
            player.on('pause', () => {
                debugLog('播放暂停');
                showStatus('⏸️ 播放已暂停', 'info');
            });
        }
        
        // 测试播放
        function testPlayback() {
            const testUrl = 'https://plyr.16bb.de/playlists/playlist_fmp4_20250723_123456.m3u8';
            document.getElementById('playlistUrl').value = testUrl;
            loadVideo();
        }
        
        // 更新统计信息
        function updateStats() {
            document.getElementById('segmentCount').textContent = segmentCount;
            document.getElementById('errorCount').textContent = errorCount;
            
            if (hls && hls.media) {
                const buffered = hls.media.buffered;
                if (buffered.length > 0) {
                    const bufferEnd = buffered.end(buffered.length - 1);
                    const currentTime = hls.media.currentTime;
                    const bufferHealth = Math.max(0, bufferEnd - currentTime).toFixed(1);
                    document.getElementById('bufferHealth').textContent = bufferHealth + 's';
                }
            }
        }
        
        // 显示统计
        function showStats() {
            if (!hls || !hls.media) {
                showStatus('请先加载视频', 'error');
                return;
            }
            
            const stats = {
                format: isMP4Format ? 'fMP4' : 'TS',
                segments: segmentCount,
                errors: errorCount,
                duration: hls.media.duration ? hls.media.duration.toFixed(2) + 's' : '未知',
                currentTime: hls.media.currentTime ? hls.media.currentTime.toFixed(2) + 's' : '0s',
                buffered: hls.media.buffered.length
            };
            
            alert(`📊 播放统计:\n格式: ${stats.format}\n片段数: ${stats.segments}\n错误数: ${stats.errors}\n总时长: ${stats.duration}\n当前时间: ${stats.currentTime}\n缓冲区数: ${stats.buffered}`);
        }
        
        // 清除视频
        function clearVideo() {
            if (hls) {
                hls.destroy();
                hls = null;
            }
            
            if (player) {
                player.stop();
                player.media.src = '';
            }
            
            errorCount = 0;
            segmentCount = 0;
            updateStats();
            
            document.getElementById('statsGrid').style.display = 'none';
            document.getElementById('formatIndicator').style.display = 'none';
            showStatus('视频已清除', 'info');
            debugLog('视频已清除');
        }
        
        // 切换调试模式
        function toggleDebug() {
            debugMode = document.getElementById('enableDebug').checked;
            const debugLog = document.getElementById('debugLog');
            debugLog.style.display = debugMode ? 'block' : 'none';
            
            if (debugMode) {
                showStatus('🔍 调试模式已启用', 'info');
            }
        }
        
        // 显示状态
        function showStatus(message, type = 'info') {
            const statusEl = document.getElementById('status');
            statusEl.textContent = message;
            statusEl.className = `status ${type}`;
            debugLog(`[${type.toUpperCase()}] ${message}`);
        }
        
        // 调试日志
        function debugLog(message) {
            if (!debugMode) return;
            
            const debugLogEl = document.getElementById('debugLog');
            const time = new Date().toLocaleTimeString();
            debugLogEl.innerHTML += `<div>[${time}] ${message}</div>`;
            debugLogEl.scrollTop = debugLogEl.scrollHeight;
        }
        
        // 页面加载完成
        window.addEventListener('load', () => {
            showStatus('🚀 fMP4播放器准备就绪', 'success');
            debugLog('页面初始化完成');
        });
    </script>
</body>
</html>